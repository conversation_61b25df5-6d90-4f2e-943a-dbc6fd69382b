#!/bin/bash

echo "🚀 开始重新打包DMG软件..."
echo "📁 项目目录: $(pwd)"

# 设置环境变量
export LANG=en_US.UTF-8
export LC_ALL=en_US.UTF-8

echo ""
echo "🧹 清理旧的构建文件..."
if [ -d "dist" ]; then
    rm -rf dist/
    echo "✅ 已删除旧的dist目录"
else
    echo "ℹ️  dist目录不存在，跳过清理"
fi

echo ""
echo "📦 开始构建应用..."
echo "⏰ 这可能需要几分钟时间，请耐心等待..."

# 构建macOS版本
echo ""
echo "🍎 构建macOS版本..."
npm run build:mac

if [ $? -eq 0 ]; then
    echo "✅ macOS版本构建成功！"
else
    echo "❌ macOS版本构建失败"
    exit 1
fi

echo ""
echo "📋 构建结果："
if [ -d "dist" ]; then
    echo "📁 dist目录内容："
    ls -la dist/
    
    echo ""
    echo "📦 DMG文件："
    find dist/ -name "*.dmg" -exec ls -lh {} \;
    
    echo ""
    echo "🔧 自动修复DMG签名..."
    for dmg in dist/*.dmg; do
        if [ -f "$dmg" ]; then
            echo "处理: $(basename "$dmg")"
            xattr -cr "$dmg" 2>/dev/null || true
            codesign --force --sign - "$dmg" 2>/dev/null || true
            echo "✅ 修复完成: $(basename "$dmg")"
        fi
    done
    
    echo ""
    echo "🎉 DMG重新打包完成！"
    echo ""
    echo "📖 文件位置："
    find dist/ -name "*.dmg" -exec echo "  - {}" \;
    
    echo ""
    echo "📖 使用说明："
    echo "1. 双击DMG文件打开安装包"
    echo "2. 将应用拖拽到Applications文件夹"
    echo "3. 如果系统提示无法验证开发者，请在系统偏好设置 > 安全性与隐私中点击'仍要打开'"
    
else
    echo "❌ 构建失败：dist目录未生成"
    exit 1
fi

echo ""
echo "✨ 重新打包完成！"
