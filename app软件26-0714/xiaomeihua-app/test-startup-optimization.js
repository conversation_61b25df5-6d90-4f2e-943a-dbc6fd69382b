#!/usr/bin/env node

/**
 * 测试启动优化效果
 * 验证有新版本时只显示更新弹窗，没有新版本时正常显示登录窗口
 */

const fs = require('fs');
const path = require('path');

console.log('🧪 测试启动优化效果...\n');

// 检查main.js中的关键修改
const mainJsPath = path.join(__dirname, 'src', 'main.js');

if (!fs.existsSync(mainJsPath)) {
    console.error('❌ main.js文件不存在');
    process.exit(1);
}

const mainJsContent = fs.readFileSync(mainJsPath, 'utf8');

console.log('📋 检查关键优化点:\n');

// 检查1: 更新检查是否在登录窗口创建之前
const hasUpdateCheckFirst = mainJsContent.includes('启动时优先检查更新') && 
                           mainJsContent.includes('发现新版本，直接显示更新弹窗，不创建登录窗口');

console.log(`1. 更新检查优先级: ${hasUpdateCheckFirst ? '✅ 已优化' : '❌ 未优化'}`);
if (hasUpdateCheckFirst) {
    console.log('   - 启动时会优先检查更新');
    console.log('   - 有更新时直接显示更新弹窗');
    console.log('   - 不会先创建登录窗口');
}

// 检查2: 登录窗口创建是否被移除
const loginWindowRemoved = !mainJsContent.includes('// 创建登录窗口\n    createLoginWindow();');

console.log(`2. 登录窗口创建时机: ${loginWindowRemoved ? '✅ 已优化' : '❌ 未优化'}`);
if (loginWindowRemoved) {
    console.log('   - 登录窗口不再在启动时立即创建');
    console.log('   - 只有在没有更新时才创建');
}

// 检查3: 条件创建逻辑
const hasConditionalCreate = mainJsContent.includes('没有更新时才创建登录窗口') &&
                            mainJsContent.includes('createLoginWindow();');

console.log(`3. 条件创建逻辑: ${hasConditionalCreate ? '✅ 已实现' : '❌ 未实现'}`);
if (hasConditionalCreate) {
    console.log('   - 只有在没有更新时才创建登录窗口');
    console.log('   - 有更新时停止正常启动流程');
}

// 检查4: 错误处理
const hasErrorHandling = mainJsContent.includes('更新检查失败，创建登录窗口以确保应用可用');

console.log(`4. 错误处理机制: ${hasErrorHandling ? '✅ 已实现' : '❌ 未实现'}`);
if (hasErrorHandling) {
    console.log('   - 更新检查失败时仍会创建登录窗口');
    console.log('   - 确保应用在任何情况下都可用');
}

// 检查5: activate事件优化
const hasActivateOptimization = mainJsContent.includes('应用激活时检查更新');

console.log(`5. 激活事件优化: ${hasActivateOptimization ? '✅ 已优化' : '❌ 未优化'}`);
if (hasActivateOptimization) {
    console.log('   - macOS激活时也会先检查更新');
    console.log('   - 保持一致的启动行为');
}

console.log('\n📝 优化总结:');

const allOptimized = hasUpdateCheckFirst && loginWindowRemoved && hasConditionalCreate && 
                    hasErrorHandling && hasActivateOptimization;

if (allOptimized) {
    console.log('✅ 所有优化点都已实现！');
    console.log('\n🎯 预期行为:');
    console.log('  - 应用启动时优先检查更新');
    console.log('  - 有新版本时：只显示更新弹窗，不显示登录窗口');
    console.log('  - 没有新版本时：正常显示登录窗口');
    console.log('  - 更新检查失败时：仍显示登录窗口，确保应用可用');
    console.log('  - macOS激活时：遵循同样的逻辑');
} else {
    console.log('❌ 部分优化点未完成，请检查代码');
}

console.log('\n🚀 准备重新打包应用...');
