#!/bin/bash

echo "🚀 开始修复DMG签名问题..."

cd "$(dirname "$0")/dist"

echo "📁 当前目录: $(pwd)"
echo "📋 DMG文件列表:"
ls -la *.dmg

echo ""
echo "🔧 开始修复..."

# 修复每个DMG文件
for dmg in *.dmg; do
    if [ -f "$dmg" ]; then
        echo "处理: $dmg"

        # 清理扩展属性
        echo "  - 清理扩展属性..."
        xattr -cr "$dmg" 2>/dev/null || true

        # 应用ad-hoc签名
        echo "  - 应用ad-hoc签名..."
        codesign --force --sign - "$dmg" 2>/dev/null || true

        # 检查文件大小
        size=$(ls -lh "$dmg" | awk '{print $5}')
        echo "  - 文件大小: $size"

        echo "  ✅ 完成: $dmg"
        echo ""
    fi
done

echo "🎉 DMG修复完成！"
echo ""
echo "📖 使用说明:"
echo "1. 双击DMG文件打开安装包"
echo "2. 将应用拖拽到Applications文件夹"
echo "3. 如果系统提示无法验证开发者，请在系统偏好设置 > 安全性与隐私中点击'仍要打开'"
echo ""
echo "📁 文件位置: $(pwd)"

echo ""
echo "🔄 重新打包DMG文件..."
echo "如果需要重新打包，请运行以下命令:"
echo "cd $(dirname "$0")"
echo "npm run build"
echo ""
echo "或者使用以下命令单独打包:"
echo "npx electron-builder --mac --publish=never"
