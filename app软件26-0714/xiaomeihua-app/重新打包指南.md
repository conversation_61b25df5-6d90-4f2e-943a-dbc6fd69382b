# 重新打包DMG软件指南

## 方法一：使用npm脚本（推荐）

### 1. 打开终端并进入项目目录
```bash
cd "app软件26-0714/xiaomeihua-app"
```

### 2. 清理旧的构建文件
```bash
rm -rf dist/
```

### 3. 重新构建macOS版本
```bash
npm run build:mac
```

### 4. 修复DMG签名
```bash
# 进入dist目录
cd dist/

# 清理扩展属性并应用签名
for dmg in *.dmg; do
    if [ -f "$dmg" ]; then
        echo "处理: $dmg"
        xattr -cr "$dmg"
        codesign --force --sign - "$dmg"
        echo "完成: $dmg"
    fi
done
```

## 方法二：使用electron-builder直接构建

### 1. 清理并构建
```bash
cd "app软件26-0714/xiaomeihua-app"
rm -rf dist/
npx electron-builder --mac --publish=never
```

### 2. 修复签名
```bash
cd dist/
xattr -cr *.dmg
codesign --force --sign - *.dmg
```

## 方法三：分步构建

### 1. 构建Intel版本
```bash
npx electron-builder --mac --x64 --publish=never
```

### 2. 构建Apple Silicon版本
```bash
npx electron-builder --mac --arm64 --publish=never
```

### 3. 构建通用版本（可选）
```bash
npx electron-builder --mac --universal --publish=never
```

## 验证构建结果

### 1. 检查DMG文件
```bash
ls -la dist/*.dmg
```

### 2. 验证签名状态
```bash
codesign -dv dist/*.dmg
```

### 3. 验证DMG完整性
```bash
hdiutil verify dist/*.dmg
```

## 构建配置说明

当前项目的构建配置（package.json）：
- **产品名称**: 小梅花AI智能客服
- **版本**: 1.0.3
- **应用ID**: cn.xiaomeihuakefu.app
- **输出目录**: dist/

### 可用的构建脚本：
- `npm run build:mac` - 构建macOS版本
- `npm run build:win` - 构建Windows版本
- `npm run build:dmg` - 仅构建DMG格式

## 常见问题解决

### 1. 构建失败
- 检查Node.js版本（推荐16+）
- 确保electron-builder已安装
- 清理node_modules并重新安装：
  ```bash
  rm -rf node_modules package-lock.json
  npm install
  ```

### 2. 签名问题
- 使用ad-hoc签名：`codesign --force --sign - file.dmg`
- 清理扩展属性：`xattr -cr file.dmg`

### 3. 权限问题
- 确保有写入dist目录的权限
- 使用sudo运行命令（如果需要）

## 最终文件位置

构建完成后，DMG文件将位于：
```
app软件26-0714/xiaomeihua-app/dist/
├── 小梅花AI智能客服-1.0.3-M芯片版本.dmg    (Apple Silicon)
├── 小梅花AI智能客服-1.0.3-x64.dmg         (Intel)
└── mac/                                    (应用程序目录)
```

## 用户安装说明

1. 双击DMG文件打开安装包
2. 将应用图标拖拽到Applications文件夹
3. 如果系统提示"无法验证开发者"：
   - 打开"系统偏好设置" > "安全性与隐私"
   - 在"通用"选项卡中点击"仍要打开"
   - 或者右键点击应用选择"打开"

## 注意事项

- 构建过程可能需要几分钟时间
- 确保网络连接正常（下载依赖）
- 建议在构建前备份重要文件
- 修复后的DMG使用ad-hoc签名，适用于本地分发
