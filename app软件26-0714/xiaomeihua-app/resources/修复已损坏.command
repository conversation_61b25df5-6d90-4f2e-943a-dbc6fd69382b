#!/bin/bash

# 小梅花AI智能客服 - 修复"已损坏"问题脚本
# 此脚本用于修复macOS显示应用程序"已损坏"的问题

APP_NAME="小梅花AI智能客服"
APP_PATH="/Applications/${APP_NAME}.app"

echo "🔧 ${APP_NAME} - 修复工具"
echo "========================================"
echo ""

# 检查应用程序是否存在
if [ ! -d "$APP_PATH" ]; then
    echo "❌ 错误: 应用程序未安装"
    echo "请先将 ${APP_NAME}.app 拖拽到 Applications 文件夹"
    echo ""
    echo "按任意键退出..."
    read -n 1
    exit 1
fi

echo "✅ 找到应用程序: $APP_PATH"
echo ""

# 显示修复选项
echo "请选择修复方式:"
echo "1. 标准修复 (推荐) - 移除隔离属性"
echo "2. 完整修复 - 移除隔离属性 + 重新签名"
echo "3. 系统设置 - 打开安全设置页面"
echo "4. 退出"
echo ""
read -p "请输入选择 (1-4): " choice

case $choice in
    1)
        echo ""
        echo "🔄 执行标准修复..."
        echo "正在移除隔离属性..."

        if sudo xattr -rd com.apple.quarantine "$APP_PATH" 2>/dev/null; then
            echo "✅ 隔离属性已移除"
        else
            echo "⚠️  隔离属性移除失败或不存在"
        fi

        echo "✅ 标准修复完成！"
        echo ""
        echo "现在可以尝试打开应用程序了。"
        echo "如果仍有问题，请尝试选项2进行完整修复。"
        ;;

    2)
        echo ""
        echo "🔄 执行完整修复..."
        echo "正在移除隔离属性..."

        if sudo xattr -rd com.apple.quarantine "$APP_PATH" 2>/dev/null; then
            echo "✅ 隔离属性已移除"
        else
            echo "⚠️  隔离属性移除失败或不存在"
        fi

        echo "正在重新签名应用程序..."
        if sudo codesign --force --deep --sign - "$APP_PATH" 2>/dev/null; then
            echo "✅ 应用程序重新签名完成"
        else
            echo "⚠️  重新签名失败，但隔离属性已移除"
        fi

        echo "✅ 完整修复完成！"
        echo ""
        echo "现在可以尝试打开应用程序了。"
        ;;

    3)
        echo ""
        echo "🔄 打开系统安全设置..."
        echo "请在打开的设置页面中："
        echo "1. 找到 '${APP_NAME}' 相关的安全提示"
        echo "2. 点击 '仍要打开' 或 '打开'"
        echo ""
        open "x-apple.systempreferences:com.apple.preference.security?General"
        ;;

    4)
        echo "退出修复工具"
        exit 0
        ;;

    *)
        echo "❌ 无效选择"
        exit 1
        ;;
esac

echo ""
echo "💡 使用提示:"
echo "- 如果应用程序仍无法打开，请重启电脑后再试"
echo "- 首次打开时可能需要在安全设置中确认"
echo "- 如有其他问题，请联系技术支持"
echo ""
echo "按任意键退出..."
read -n 1
