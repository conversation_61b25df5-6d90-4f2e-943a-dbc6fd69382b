# 小梅花AI智能客服 - 完整代码签名指南

## 概述

本指南将帮助您完成Apple Developer ID证书申请和应用代码签名的完整流程，确保应用可以在macOS上正常安装，不会提示"已损坏"。

## 🎯 目标

- ✅ 申请免费的Apple Developer ID证书
- ✅ 配置代码签名环境
- ✅ 构建签名的应用程序
- ✅ 确保应用启动速度不受影响
- ✅ 提供用户友好的安装体验

## 📋 前置要求

- macOS 系统（10.15+）
- 有效的Apple ID
- Xcode Command Line Tools
- 网络连接

## 🚀 快速开始

### 方法一：自动化脚本（推荐）

```bash
# 1. 检查和设置代码签名环境
npm run setup:codesign

# 2. 如果没有证书，运行证书助手
node scripts/certificate-helper.js

# 3. 构建签名版本
npm run build:signed

# 4. 验证签名
npm run verify:codesign

# 5. 自动打包发布
npm run auto-sign-package
```

### 方法二：手动步骤

#### 步骤1：申请Apple Developer ID证书

1. **访问Apple Developer网站**
   - 打开 https://developer.apple.com/account/
   - 使用Apple ID登录

2. **注册开发者账户**
   - 选择"个人"账户类型（免费）
   - 同意开发者协议
   - 填写个人信息

3. **生成证书签名请求(CSR)**
   - 打开"钥匙串访问"应用
   - 菜单栏：钥匙串访问 > 证书助理 > 从证书颁发机构请求证书
   - 填写邮箱和姓名
   - 选择"存储到磁盘"

4. **创建Developer ID证书**
   - 在开发者网站点击"Certificates, Identifiers & Profiles"
   - 选择"Certificates" > "+"
   - 选择"Developer ID Application"
   - 上传CSR文件
   - 下载生成的证书

5. **安装证书**
   - 双击下载的.cer文件
   - 选择"登录"钥匙串
   - 点击"添加"

#### 步骤2：配置项目

```bash
# 验证证书安装
security find-identity -v -p codesigning

# 配置项目
npm run setup:codesign
```

#### 步骤3：构建和签名

```bash
# 构建签名版本
npm run build:signed:universal

# 或者指定架构
npm run build:signed:x64
npm run build:signed:arm64
```

## 🔧 可用命令

| 命令 | 描述 |
|------|------|
| `npm run setup:codesign` | 设置代码签名环境 |
| `npm run build:signed` | 构建签名版本 |
| `npm run verify:codesign` | 验证代码签名 |
| `npm run auto-sign-package` | 自动签名和打包 |
| `node scripts/certificate-helper.js` | 证书申请助手 |

## 📦 构建选项

### 架构选择

- `universal` - 通用版本（Intel + Apple Silicon）
- `x64` - Intel 64位版本
- `arm64` - Apple Silicon版本

### 构建命令示例

```bash
# 构建通用版本
npm run auto-sign-package universal

# 构建Intel版本
npm run auto-sign-package x64

# 无签名构建（测试用）
npm run auto-sign-package -- --no-sign

# 详细输出
npm run auto-sign-package -- --verbose
```

## 🛠️ 故障排除

### 常见问题

#### Q1: 找不到有效证书
```bash
# 检查证书状态
security find-identity -v -p codesigning

# 重新运行设置
npm run setup:codesign
```

#### Q2: 证书不被信任
- 确保证书是从Apple Developer网站下载的
- 检查证书是否过期
- 重新安装证书

#### Q3: 构建失败
```bash
# 清理并重新构建
npm run clean
npm install
npm run build:signed
```

#### Q4: 应用仍提示已损坏
- 检查签名状态：`npm run verify:codesign`
- 确保使用的是Apple Developer ID证书
- 考虑申请公证服务（需要付费开发者账户）

### 调试命令

```bash
# 检查应用签名
codesign -dv --verbose=4 "dist/小梅花AI智能客服.app"

# 验证签名
codesign --verify --deep --strict "dist/小梅花AI智能客服.app"

# 检查Gatekeeper状态
spctl --assess --type execute "dist/小梅花AI智能客服.app"
```

## 📋 文件结构

```
xiaomeihua-app/
├── scripts/
│   ├── setup-codesigning.sh      # 环境设置脚本
│   ├── build-signed.js           # 签名构建脚本
│   ├── verify-codesigning.js     # 签名验证脚本
│   ├── auto-sign-and-package.js  # 自动打包脚本
│   └── certificate-helper.js     # 证书助手
├── build/
│   └── entitlements.mac.plist    # 应用权限配置
├── docs/
│   ├── 申请Apple开发者证书指南.md
│   └── 完整代码签名指南.md
├── dist/                         # 构建输出
├── release/                      # 发布文件
├── .codesign-config             # 证书配置
└── .env.codesign               # 环境配置
```

## 🔒 安全注意事项

1. **保护私钥**：不要分享或上传私钥文件
2. **证书管理**：定期检查证书有效期
3. **权限最小化**：只申请必要的应用权限
4. **测试验证**：在不同macOS版本上测试应用

## 📈 性能优化

### 启动速度优化

代码签名不会显著影响应用启动速度，但可以通过以下方式优化：

1. **减少签名验证**：
   - 使用`hardenedRuntime: true`
   - 优化entitlements配置

2. **资源优化**：
   - 压缩应用资源
   - 使用asar打包

3. **缓存优化**：
   - 启用系统缓存
   - 减少首次启动时的网络请求

## 🎉 成功标志

当您看到以下输出时，说明代码签名配置成功：

```
✅ 证书验证通过
✅ 应用构建完成
✅ 签名验证通过
✅ DMG文件已生成
🎉 自动签名和打包完成！
```

## 📞 技术支持

如果遇到问题：

1. 查看构建日志：`cat build-report-signed.json`
2. 检查验证报告：`cat codesign-verification-report.json`
3. 运行诊断：`npm run verify:codesign`

## 📚 相关资源

- [Apple Developer Documentation](https://developer.apple.com/documentation/)
- [Code Signing Guide](https://developer.apple.com/library/archive/documentation/Security/Conceptual/CodeSigningGuide/)
- [Electron Code Signing](https://www.electronjs.org/docs/latest/tutorial/code-signing)

---

**最后更新**: 2025年7月30日  
**版本**: 1.0.0
