# Apple Developer ID 证书申请指南

## 概述
Apple Developer ID 是苹果提供的免费代码签名证书，用于在 Mac App Store 外分发应用程序。使用此证书签名的应用可以通过 macOS 的 Gatekeeper 安全检查。

## 前提条件
- macOS 系统
- 有效的 Apple ID
- Xcode 或 Xcode Command Line Tools

## 步骤一：准备 Apple ID

### 1.1 创建或登录 Apple ID
1. 访问 [Apple ID 官网](https://appleid.apple.com/)
2. 如果没有 Apple ID，点击"创建您的 Apple ID"
3. 填写必要信息并验证邮箱

### 1.2 启用双重认证
1. 登录 Apple ID 账户页面
2. 在"安全"部分启用双重认证
3. 添加受信任的设备和电话号码

## 步骤二：申请开发者账户

### 2.1 访问开发者网站
1. 打开 [Apple Developer](https://developer.apple.com/)
2. 点击右上角"Account"
3. 使用您的 Apple ID 登录

### 2.2 注册开发者账户
1. 首次登录会提示注册开发者账户
2. 选择"个人"账户类型（免费）
3. 同意开发者协议
4. 填写个人信息

## 步骤三：生成证书签名请求 (CSR)

### 3.1 打开钥匙串访问
1. 在 macOS 中打开"钥匙串访问"应用
2. 菜单栏选择"钥匙串访问" > "证书助理" > "从证书颁发机构请求证书"

### 3.2 创建 CSR 文件
1. 用户电子邮件地址：输入您的 Apple ID 邮箱
2. 常用名称：输入您的姓名
3. CA 电子邮件地址：留空
4. 选择"存储到磁盘"
5. 点击"继续"并保存 CSR 文件

## 步骤四：创建开发者证书

### 4.1 访问证书页面
1. 登录 [Apple Developer](https://developer.apple.com/account/)
2. 点击"Certificates, Identifiers & Profiles"
3. 在左侧菜单选择"Certificates"

### 4.2 创建新证书
1. 点击"+"按钮创建新证书
2. 在"Software"部分选择"Developer ID Application"
3. 点击"Continue"

### 4.3 上传 CSR 文件
1. 点击"Choose File"上传之前创建的 CSR 文件
2. 点击"Continue"
3. 证书生成后，点击"Download"下载证书文件

## 步骤五：安装证书

### 5.1 安装到钥匙串
1. 双击下载的证书文件（.cer 格式）
2. 选择"登录"钥匙串
3. 点击"添加"

### 5.2 验证证书安装
1. 在钥匙串访问中查看"我的证书"
2. 应该能看到"Developer ID Application: [您的姓名]"证书
3. 展开证书，确保有对应的私钥

## 步骤六：验证证书可用性

### 6.1 命令行验证
打开终端，运行以下命令：
```bash
security find-identity -v -p codesigning
```

应该能看到类似输出：
```
1) [证书ID] "Developer ID Application: [您的姓名] ([Team ID])"
   1 valid identities found
```

### 6.2 获取证书信息
记录以下信息，后续配置需要：
- 证书名称：Developer ID Application: [您的姓名]
- Team ID：括号中的字符串
- 证书 SHA-1 指纹

## 常见问题

### Q1: 证书申请失败
- 确保 Apple ID 已启用双重认证
- 检查开发者协议是否已同意
- 重新生成 CSR 文件

### Q2: 证书无法安装
- 确保 CSR 是在同一台 Mac 上生成的
- 检查钥匙串访问权限
- 尝试重启钥匙串访问应用

### Q3: 找不到有效证书
- 确保证书安装在"登录"钥匙串中
- 检查证书是否过期
- 验证私钥是否存在

## 下一步
证书安装完成后，您可以：
1. 配置 Electron 应用的代码签名
2. 更新构建脚本
3. 测试应用签名和分发

## 注意事项
- Developer ID 证书有效期为 5 年
- 证书是免费的，但有使用限制
- 签名的应用仍需要用户手动允许运行
- 考虑申请付费开发者账户以获得公证服务
