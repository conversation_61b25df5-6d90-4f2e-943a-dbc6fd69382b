#!/usr/bin/env node

/**
 * 构建v1.0.2版本（删除设置中的更新检查功能）
 */

const { spawn } = require('child_process');
const fs = require('fs');

console.log('🚀 开始构建小梅花AI智能客服 v1.0.2...\n');
console.log('📝 本次更新内容:');
console.log('  - 删除了软件设置中的应用更新检查功能');
console.log('  - 保持强制更新机制，由网站后台完全控制');
console.log('  - 简化了启动时更新检查逻辑\n');

// 检查package.json中的版本号
const packageJsonPath = './package.json';
if (fs.existsSync(packageJsonPath)) {
    const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
    console.log(`📦 当前版本: ${packageJson.version}`);
}

console.log('\n🏗️  开始构建 Universal DMG...\n');

// 运行构建命令
const buildProcess = spawn('npm', ['run', 'build:dmg:universal'], {
    stdio: 'inherit',
    shell: true
});

buildProcess.on('close', (code) => {
    if (code === 0) {
        console.log('\n✅ 构建成功完成！');
        
        // 检查输出文件
        const distDir = './dist';
        
        console.log('\n📋 构建结果:');
        
        if (fs.existsSync(distDir)) {
            console.log('📁 dist 目录内容:');
            const distFiles = fs.readdirSync(distDir).filter(f => f.endsWith('.dmg'));
            distFiles.forEach(file => {
                const filePath = `${distDir}/${file}`;
                const stats = fs.statSync(filePath);
                const fileSizeMB = (stats.size / (1024 * 1024)).toFixed(2);
                console.log(`  - ${file} (${fileSizeMB} MB)`);
            });
        }
        
        console.log('\n🎉 应用打包完成！');
        console.log('📝 主要变更:');
        console.log('  ✅ 删除了软件设置中的更新检查功能');
        console.log('  ✅ 保持强制更新机制不变');
        console.log('  ✅ 简化了代码结构');
        console.log('  ✅ 版本号更新为 1.0.2');
        
        console.log('\n🔧 强制更新机制说明:');
        console.log('  - 应用启动时会静默检查更新');
        console.log('  - 网站后台发布新版本时，应用会强制更新');
        console.log('  - 用户无法在设置中关闭更新检查');
        console.log('  - 确保所有用户都能及时获得最新版本');
        
    } else {
        console.error(`\n❌ 构建失败，退出码: ${code}`);
        process.exit(code);
    }
});

buildProcess.on('error', (error) => {
    console.error('❌ 构建过程出错:', error.message);
    process.exit(1);
});
