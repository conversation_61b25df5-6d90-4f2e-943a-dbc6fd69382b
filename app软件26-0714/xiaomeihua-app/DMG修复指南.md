# DMG签名修复指南

## 问题描述
DMG文件在打包时可能出现签名问题，导致用户无法正常安装应用。

## 修复方法

### 方法一：使用修复脚本（推荐）

1. 打开终端
2. 进入项目目录：
   ```bash
   cd "app软件26-0714/xiaomeihua-app"
   ```
3. 运行修复脚本：
   ```bash
   chmod +x fix-dmg.sh
   ./fix-dmg.sh
   ```

### 方法二：手动修复

1. 进入dist目录：
   ```bash
   cd "app软件26-0714/xiaomeihua-app/dist"
   ```

2. 清理扩展属性：
   ```bash
   xattr -cr "小梅花AI智能客服-1.0.3-M芯片版本.dmg"
   xattr -cr "小梅花AI智能客服-1.0.3-x64.dmg"
   ```

3. 应用ad-hoc签名：
   ```bash
   codesign --force --sign - "小梅花AI智能客服-1.0.3-M芯片版本.dmg"
   codesign --force --sign - "小梅花AI智能客服-1.0.3-x64.dmg"
   ```

### 方法三：重新打包

如果修复无效，可以重新打包DMG：

1. 进入项目目录：
   ```bash
   cd "app软件26-0714/xiaomeihua-app"
   ```

2. 重新构建：
   ```bash
   npm run build
   ```

或者单独打包macOS版本：
```bash
npx electron-builder --mac --publish=never
```

## 验证修复

修复完成后，可以通过以下方式验证：

1. 检查签名状态：
   ```bash
   codesign -dv "小梅花AI智能客服-1.0.3-M芯片版本.dmg"
   ```

2. 验证DMG完整性：
   ```bash
   hdiutil verify "小梅花AI智能客服-1.0.3-M芯片版本.dmg"
   ```

## 用户安装说明

修复后的DMG文件安装步骤：

1. 双击DMG文件打开安装包
2. 将应用图标拖拽到Applications文件夹
3. 如果系统提示"无法验证开发者"：
   - 打开"系统偏好设置" > "安全性与隐私"
   - 在"通用"选项卡中点击"仍要打开"
   - 或者在应用上右键选择"打开"

## 注意事项

- 修复后的DMG使用ad-hoc签名，适用于本地分发
- 如需App Store分发，需要使用Apple Developer证书重新签名
- 建议在修复前备份原始DMG文件

## 文件位置

- 修复脚本：`app软件26-0714/xiaomeihua-app/fix-dmg.sh`
- DMG文件：`app软件26-0714/xiaomeihua-app/dist/`
- 构建配置：`app软件26-0714/xiaomeihua-app/package.json`
