{"verificationTime": "2025-07-30T07:59:48.322Z", "summary": {"totalFiles": 3, "signedFiles": 0, "validFiles": 0, "unsignedFiles": 3}, "files": [{"file": "小梅花AI智能客服-1.0.0-arm64.dmg", "type": "dmg", "signed": false, "valid": false, "certificate": null, "teamId": null, "errors": ["Command failed: codesign -dv \"/Users/<USER>/Desktop/0最新版网站后台源码/26号app设置开发/27号/弹窗协议都正常/没有问题版本/app设置接口30-1337/app软件26-0714/xiaomeihua-app/dist/小梅花AI智能客服-1.0.0-arm64.dmg\" 2>&1"], "gatekeeper": {"quarantined": false, "gatekeeperFriendly": false}}, {"file": "小梅花AI智能客服-1.0.0-universal.dmg", "type": "dmg", "signed": false, "valid": false, "certificate": null, "teamId": null, "errors": ["Command failed: codesign -dv \"/Users/<USER>/Desktop/0最新版网站后台源码/26号app设置开发/27号/弹窗协议都正常/没有问题版本/app设置接口30-1337/app软件26-0714/xiaomeihua-app/dist/小梅花AI智能客服-1.0.0-universal.dmg\" 2>&1"], "gatekeeper": {"quarantined": false, "gatekeeperFriendly": false}}, {"file": "小梅花AI智能客服-1.0.0-x64.dmg", "type": "dmg", "signed": false, "valid": false, "certificate": null, "teamId": null, "errors": ["Command failed: codesign -dv \"/Users/<USER>/Desktop/0最新版网站后台源码/26号app设置开发/27号/弹窗协议都正常/没有问题版本/app设置接口30-1337/app软件26-0714/xiaomeihua-app/dist/小梅花AI智能客服-1.0.0-x64.dmg\" 2>&1"], "gatekeeper": {"quarantined": false, "gatekeeperFriendly": false}}], "recommendations": ["建议对未签名的文件进行代码签名"]}