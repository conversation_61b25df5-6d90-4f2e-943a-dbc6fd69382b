# DMG打包规则说明

## 打包要求

根据用户要求，DMG打包已按照以下规则进行配置：

### 1. 版本要求
- ✅ **只打包M版本和Intel版本**
  - M版本：arm64架构（适用于Apple Silicon芯片）
  - Intel版本：x64架构（适用于Intel芯片）
  - ❌ 不再打包universal版本或其他版本

### 2. DMG窗口配置
- ✅ **窗口大小**：640x480像素
- ✅ **窗口位置**：X:400 Y:200（距离屏幕左边400像素，距离屏幕顶部200像素）
- ✅ **DMG文件存放位置**：dist文件夹中

### 3. 文件命名规则
- M版本：`小梅花AI智能客服-{版本号}-M芯片版本.dmg`
- Intel版本：`小梅花AI智能客服-{版本号}-Intel版本.dmg`

### 4. DMG内容布局
- ✅ **软件和Applications在上面**：应用程序图标和Applications文件夹位于DMG窗口上方
- ✅ **Mac电脑安装教程.png在底部中间**：安装教程图片位于DMG窗口底部中间位置
- ✅ **只包含必要文件**：不包含任何多余的DMG文件

## 使用方法

### 快速打包命令
```bash
# 打包M版本和Intel版本（推荐使用）
npm run build:m-intel
```

### 传统打包命令（如需要）
```bash
# 单独打包M版本
npm run build:dmg:arm64

# 单独打包Intel版本  
npm run build:dmg:x64
```

## 打包流程

1. **自动清理**：清理dist目录中的旧DMG文件
2. **构建M版本**：构建arm64架构的DMG
3. **构建Intel版本**：构建x64架构的DMG
4. **验证文件**：自动验证生成的DMG文件完整性
5. **显示结果**：显示文件大小和位置信息

## 输出示例

```
🎉 DMG打包完成！

📦 生成的文件:
  ✅ 小梅花AI智能客服-1.0.3-M芯片版本.dmg (M芯片版本) (100.6MB)
  ✅ 小梅花AI智能客服-1.0.3-Intel版本.dmg (Intel版本) (105.4MB)

📁 文件位置: /path/to/project/dist

📋 打包配置:
  - 窗口大小: 640x480像素
  - 窗口位置: X:400 Y:200
  - 包含Mac电脑安装教程.png图片在底部中间
  - 软件和Applications在上面
  - 只包含: M芯片版本 + Intel版本
```

## 配置文件

### package.json配置
DMG相关配置已在`package.json`中设置：

```json
{
  "build": {
    "mac": {
      "target": [
        {
          "target": "dmg",
          "arch": ["x64", "arm64"]
        }
      ]
    },
    "dmg": {
      "window": {
        "width": 640,
        "height": 480,
        "x": 400,
        "y": 200
      }
    }
  }
}
```

### 脚本文件
- 主要脚本：`scripts/build-m-intel-dmg.js`
- 功能：专门用于打包M版本和Intel版本

## 注意事项

1. **系统要求**：建议在macOS系统上运行以获得最佳体验
2. **依赖检查**：脚本会自动检查和安装必要的依赖
3. **文件验证**：生成的DMG文件会自动进行完整性验证
4. **清理机制**：每次打包前会自动清理旧的DMG文件

## 故障排除

### 常见问题
1. **构建失败**：检查网络连接和依赖安装
2. **签名警告**：这是正常现象，不影响DMG文件的使用
3. **权限问题**：确保有足够的文件系统权限

### 获取帮助
```bash
# 查看帮助信息
node scripts/build-m-intel-dmg.js --help
```

## 更新记录

- **2025-01-30**：创建专用的M版本和Intel版本打包脚本
- 配置DMG窗口大小为640x480像素
- 配置DMG窗口位置为X:400 Y:200
- 添加Mac电脑安装教程.png图片在底部中间
- 软件和Applications放在上面
- 文件命名优化：小梅花AI智能客服-xxxx-M芯片版本.dmg 和 小梅花AI智能客服-xxxx-Intel版本.dmg
- 移除其他版本的打包，只保留M版本和Intel版本
- 自动重命名DMG文件为规范格式
