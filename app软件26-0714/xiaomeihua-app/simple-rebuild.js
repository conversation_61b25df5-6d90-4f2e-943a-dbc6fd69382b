const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 开始重新打包DMG软件...');

try {
    // 清理旧文件
    console.log('🧹 清理旧的构建文件...');
    if (fs.existsSync('dist')) {
        fs.rmSync('dist', { recursive: true, force: true });
        console.log('✅ 已删除旧的dist目录');
    }

    // 构建应用
    console.log('📦 开始构建macOS版本...');
    console.log('⏰ 这可能需要几分钟时间，请耐心等待...');
    
    execSync('npm run build:mac', { stdio: 'inherit' });
    
    console.log('✅ 构建完成！');

    // 检查结果
    if (fs.existsSync('dist')) {
        console.log('📋 构建结果：');
        const files = fs.readdirSync('dist');
        files.forEach(file => {
            const filePath = path.join('dist', file);
            const stats = fs.statSync(filePath);
            if (file.endsWith('.dmg')) {
                const size = (stats.size / 1024 / 1024).toFixed(2);
                console.log(`📦 ${file} (${size} MB)`);
            } else if (stats.isDirectory()) {
                console.log(`📁 ${file}/`);
            }
        });

        // 自动修复DMG签名
        console.log('\n🔧 修复DMG签名...');
        const dmgFiles = files.filter(f => f.endsWith('.dmg'));
        dmgFiles.forEach(dmg => {
            const dmgPath = path.join('dist', dmg);
            console.log(`处理: ${dmg}`);
            try {
                execSync(`xattr -cr "${dmgPath}"`, { stdio: 'pipe' });
                execSync(`codesign --force --sign - "${dmgPath}"`, { stdio: 'pipe' });
                console.log(`✅ 修复完成: ${dmg}`);
            } catch (error) {
                console.log(`⚠️  修复失败: ${dmg}`);
            }
        });

        console.log('\n🎉 重新打包完成！');
        console.log('\n📖 使用说明：');
        console.log('1. 双击DMG文件打开安装包');
        console.log('2. 将应用拖拽到Applications文件夹');
        console.log('3. 如果系统提示无法验证开发者，请在系统偏好设置中允许');
    } else {
        console.log('❌ 构建失败：未生成dist目录');
    }

} catch (error) {
    console.error('❌ 构建失败:', error.message);
    console.log('\n📖 手动构建方法：');
    console.log('1. 删除dist目录: rm -rf dist/');
    console.log('2. 运行构建: npm run build:mac');
    console.log('3. 修复签名: cd dist/ && xattr -cr *.dmg && codesign --force --sign - *.dmg');
}
