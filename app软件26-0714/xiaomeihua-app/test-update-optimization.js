#!/usr/bin/env node

/**
 * 测试更新优化效果
 * 验证启动时不再每次都弹出"当前已是最新版本"提示
 */

const fs = require('fs');
const path = require('path');

console.log('🧪 开始测试更新优化效果...\n');

// 测试文件路径
const testFiles = [
    {
        path: path.join(__dirname, 'src/main.js'),
        name: 'main.js',
        tests: [
            {
                name: '检查启动时更新检查逻辑',
                pattern: /启动时静默检查更新/,
                expected: true,
                description: '应该包含静默检查更新的逻辑'
            },
            {
                name: '检查用户设置读取',
                pattern: /store\.get\('autoCheckUpdate'/,
                expected: true,
                description: '应该读取用户的自动检查更新设置'
            },
            {
                name: '检查设置同步IPC处理器',
                pattern: /get-app-setting.*set-app-setting/s,
                expected: true,
                description: '应该包含设置同步的IPC处理器'
            }
        ]
    },
    {
        path: path.join(__dirname, 'src/preload.js'),
        name: 'preload.js',
        tests: [
            {
                name: '检查更新API暴露',
                pattern: /checkForUpdates.*getAppSetting.*setAppSetting/s,
                expected: true,
                description: '应该暴露更新和设置相关的API'
            }
        ]
    },
    {
        path: path.join(__dirname, 'src/renderer/main.html'),
        name: 'main.html',
        tests: [
            {
                name: '检查设置界面元素',
                pattern: /auto-check-update.*manual-check-update-btn/s,
                expected: true,
                description: '应该包含更新设置的界面元素'
            },
            {
                name: '检查开关样式',
                pattern: /settings-switch.*settings-slider/s,
                expected: true,
                description: '应该包含开关控件的样式'
            },
            {
                name: '检查设置初始化函数',
                pattern: /initializeAppSettings/,
                expected: true,
                description: '应该包含设置初始化函数'
            }
        ]
    }
];

let totalTests = 0;
let passedTests = 0;
let failedTests = [];

// 执行测试
testFiles.forEach(file => {
    console.log(`📁 测试文件: ${file.name}`);
    
    if (!fs.existsSync(file.path)) {
        console.log(`❌ 文件不存在: ${file.path}\n`);
        return;
    }
    
    const content = fs.readFileSync(file.path, 'utf8');
    
    file.tests.forEach(test => {
        totalTests++;
        const result = test.pattern.test(content);
        
        if (result === test.expected) {
            console.log(`✅ ${test.name}`);
            passedTests++;
        } else {
            console.log(`❌ ${test.name}`);
            failedTests.push({
                file: file.name,
                test: test.name,
                description: test.description,
                expected: test.expected,
                actual: result
            });
        }
    });
    
    console.log('');
});

// 输出测试结果
console.log('📊 测试结果汇总:');
console.log(`总测试数: ${totalTests}`);
console.log(`通过: ${passedTests}`);
console.log(`失败: ${failedTests.length}`);

if (failedTests.length > 0) {
    console.log('\n❌ 失败的测试:');
    failedTests.forEach(failure => {
        console.log(`  - ${failure.file}: ${failure.test}`);
        console.log(`    ${failure.description}`);
        console.log(`    期望: ${failure.expected}, 实际: ${failure.actual}`);
    });
} else {
    console.log('\n🎉 所有测试都通过了！');
}

// 检查关键优化点
console.log('\n🔍 关键优化点检查:');

const mainJsContent = fs.readFileSync(path.join(__dirname, 'src/main.js'), 'utf8');

// 检查是否移除了非静默检查
const hasNonSilentCheck = /checkForUpdates\(false\)/.test(mainJsContent);
if (!hasNonSilentCheck) {
    console.log('✅ 已移除启动时的非静默更新检查');
} else {
    console.log('⚠️  仍然存在启动时的非静默更新检查');
}

// 检查是否添加了用户设置控制
const hasUserSettingControl = /autoCheckUpdate.*store\.get/.test(mainJsContent);
if (hasUserSettingControl) {
    console.log('✅ 已添加用户设置控制启动时更新检查');
} else {
    console.log('⚠️  缺少用户设置控制逻辑');
}

console.log('\n🏁 测试完成！');

// 返回适当的退出码
process.exit(failedTests.length > 0 ? 1 : 0);
