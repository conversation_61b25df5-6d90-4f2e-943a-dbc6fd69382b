#!/usr/bin/env node

/**
 * 构建v1.0.3版本（优化启动时更新检查优先级）
 */

const { spawn } = require('child_process');
const fs = require('fs');

console.log('🚀 开始构建小梅花AI智能客服 v1.0.3...\n');
console.log('📝 本次更新内容:');
console.log('  ✅ 优化启动时更新检查优先级');
console.log('  ✅ 有新版本时直接显示更新弹窗，不显示登录窗口');
console.log('  ✅ 避免用户先登录再被更新弹窗打断');
console.log('  ✅ 保持强制更新机制不变\n');

// 检查package.json中的版本号
const packageJsonPath = './package.json';
if (fs.existsSync(packageJsonPath)) {
    const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
    console.log(`📦 当前版本: ${packageJson.version}`);
}

console.log('\n🏗️  开始构建 Universal DMG...\n');

// 运行构建命令
const buildProcess = spawn('npm', ['run', 'build:dmg:universal'], {
    stdio: 'inherit',
    shell: true
});

buildProcess.on('close', (code) => {
    if (code === 0) {
        console.log('\n✅ 构建成功完成！');
        
        // 检查输出文件
        const distDir = './dist';
        
        console.log('\n📋 构建结果:');
        
        if (fs.existsSync(distDir)) {
            console.log('📁 dist 目录内容:');
            const distFiles = fs.readdirSync(distDir).filter(f => f.endsWith('.dmg'));
            distFiles.forEach(file => {
                const filePath = `${distDir}/${file}`;
                const stats = fs.statSync(filePath);
                const fileSizeMB = (stats.size / (1024 * 1024)).toFixed(2);
                console.log(`  - ${file} (${fileSizeMB} MB)`);
            });
        }
        
        console.log('\n🎉 应用打包完成！');
        console.log('📝 主要优化:');
        console.log('  ✅ 启动时优先检查更新');
        console.log('  ✅ 有更新时直接显示更新弹窗');
        console.log('  ✅ 没有更新时才显示登录窗口');
        console.log('  ✅ 版本号更新为 1.0.3');
        
        console.log('\n🔧 优化后的启动流程:');
        console.log('  1. 应用启动');
        console.log('  2. 初始化各种管理器');
        console.log('  3. 优先检查更新');
        console.log('  4. 如果有更新：显示更新弹窗，停止启动流程');
        console.log('  5. 如果没有更新：创建登录窗口，正常启动');
        
        console.log('\n🎯 用户体验改进:');
        console.log('  - 有新版本时不会看到登录窗口');
        console.log('  - 避免登录后被更新弹窗打断');
        console.log('  - 更新流程更加流畅');
        
    } else {
        console.error(`\n❌ 构建失败，退出码: ${code}`);
        process.exit(code);
    }
});

buildProcess.on('error', (error) => {
    console.error('❌ 构建过程出错:', error.message);
    process.exit(1);
});
