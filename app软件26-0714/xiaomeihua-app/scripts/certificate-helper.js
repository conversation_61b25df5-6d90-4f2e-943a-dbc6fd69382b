#!/usr/bin/env node

/**
 * Apple Developer ID 证书申请助手
 * 简化证书申请和配置流程
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');
const readline = require('readline');

class CertificateHelper {
    constructor() {
        this.projectRoot = path.resolve(__dirname, '..');
        this.configFile = path.join(this.projectRoot, '.codesign-config');
        this.rl = readline.createInterface({
            input: process.stdin,
            output: process.stdout
        });
    }

    /**
     * 询问用户输入
     */
    async askQuestion(question) {
        return new Promise((resolve) => {
            this.rl.question(question, (answer) => {
                resolve(answer.trim());
            });
        });
    }

    /**
     * 检查系统环境
     */
    checkEnvironment() {
        console.log('🔍 检查系统环境...');
        
        if (process.platform !== 'darwin') {
            console.log('❌ 此工具只能在 macOS 上使用');
            return false;
        }
        
        try {
            execSync('which security', { stdio: 'pipe' });
            execSync('which codesign', { stdio: 'pipe' });
            console.log('✅ 系统环境检查通过');
            return true;
        } catch (error) {
            console.log('❌ 缺少必要的系统工具');
            return false;
        }
    }

    /**
     * 检查现有证书
     */
    checkExistingCertificates() {
        console.log('\n🔍 检查现有证书...');
        
        try {
            const result = execSync('security find-identity -v -p codesigning', { encoding: 'utf8' });
            const lines = result.split('\n').filter(line => line.includes('Developer ID Application'));
            
            if (lines.length > 0) {
                console.log('✅ 找到以下 Developer ID 证书:');
                lines.forEach((line, index) => {
                    const match = line.match(/"(.+)"/);
                    if (match) {
                        console.log(`   ${index + 1}. ${match[1]}`);
                    }
                });
                return lines;
            } else {
                console.log('⚠️  未找到 Developer ID Application 证书');
                return [];
            }
        } catch (error) {
            console.log('❌ 检查证书时出错');
            return [];
        }
    }

    /**
     * 选择证书
     */
    async selectCertificate(certificates) {
        if (certificates.length === 0) {
            return null;
        }
        
        if (certificates.length === 1) {
            const match = certificates[0].match(/"(.+)"/);
            return match ? match[1] : null;
        }
        
        console.log('\n请选择要使用的证书:');
        certificates.forEach((cert, index) => {
            const match = cert.match(/"(.+)"/);
            if (match) {
                console.log(`${index + 1}. ${match[1]}`);
            }
        });
        
        const choice = await this.askQuestion('请输入证书编号 (1-' + certificates.length + '): ');
        const index = parseInt(choice) - 1;
        
        if (index >= 0 && index < certificates.length) {
            const match = certificates[index].match(/"(.+)"/);
            return match ? match[1] : null;
        }
        
        return null;
    }

    /**
     * 保存证书配置
     */
    saveCertificateConfig(certName) {
        const config = `DEVELOPER_ID_CERT="${certName}"`;
        fs.writeFileSync(this.configFile, config);
        console.log(`✅ 证书配置已保存: ${this.configFile}`);
    }

    /**
     * 生成 CSR 文件
     */
    async generateCSR() {
        console.log('\n📝 生成证书签名请求 (CSR)...');
        
        const email = await this.askQuestion('请输入您的 Apple ID 邮箱: ');
        const name = await this.askQuestion('请输入您的姓名: ');
        
        if (!email || !name) {
            console.log('❌ 邮箱和姓名不能为空');
            return false;
        }
        
        try {
            // 使用钥匙串访问生成 CSR
            const csrPath = path.join(this.projectRoot, 'CertificateSigningRequest.certSigningRequest');
            
            console.log('🔧 正在生成 CSR 文件...');
            console.log('请在钥匙串访问中完成以下操作:');
            console.log('1. 打开"钥匙串访问"应用');
            console.log('2. 菜单栏选择"钥匙串访问" > "证书助理" > "从证书颁发机构请求证书"');
            console.log('3. 填写以下信息:');
            console.log(`   - 用户电子邮件地址: ${email}`);
            console.log(`   - 常用名称: ${name}`);
            console.log(`   - CA 电子邮件地址: 留空`);
            console.log('4. 选择"存储到磁盘"');
            console.log(`5. 保存为: ${csrPath}`);
            
            await this.askQuestion('完成后按回车键继续...');
            
            if (fs.existsSync(csrPath)) {
                console.log(`✅ CSR 文件已生成: ${csrPath}`);
                return true;
            } else {
                console.log('❌ 未找到 CSR 文件');
                return false;
            }
        } catch (error) {
            console.log(`❌ 生成 CSR 失败: ${error.message}`);
            return false;
        }
    }

    /**
     * 显示申请指导
     */
    showApplicationGuide() {
        console.log('\n📋 Apple Developer ID 申请指导');
        console.log('='.repeat(50));
        console.log('');
        console.log('1. 🌐 访问 Apple Developer 网站:');
        console.log('   https://developer.apple.com/account/');
        console.log('');
        console.log('2. 🔐 登录您的 Apple ID');
        console.log('   - 如果没有 Apple ID，请先注册');
        console.log('   - 确保启用了双重认证');
        console.log('');
        console.log('3. 📝 注册开发者账户');
        console.log('   - 选择"个人"账户类型（免费）');
        console.log('   - 同意开发者协议');
        console.log('   - 填写个人信息');
        console.log('');
        console.log('4. 📜 创建证书');
        console.log('   - 点击"Certificates, Identifiers & Profiles"');
        console.log('   - 选择"Certificates" > "+"');
        console.log('   - 选择"Developer ID Application"');
        console.log('   - 上传 CSR 文件');
        console.log('   - 下载生成的证书文件');
        console.log('');
        console.log('5. 💾 安装证书');
        console.log('   - 双击下载的 .cer 文件');
        console.log('   - 选择"登录"钥匙串');
        console.log('   - 点击"添加"');
        console.log('');
        console.log('6. ✅ 验证安装');
        console.log('   - 重新运行此工具');
        console.log('   - 选择已安装的证书');
        console.log('');
    }

    /**
     * 打开相关网站
     */
    async openWebsites() {
        const openSites = await this.askQuestion('是否打开 Apple Developer 网站？(y/n): ');
        
        if (openSites.toLowerCase() === 'y') {
            try {
                execSync('open https://developer.apple.com/account/');
                console.log('✅ 已打开 Apple Developer 网站');
            } catch (error) {
                console.log('❌ 无法打开网站，请手动访问: https://developer.apple.com/account/');
            }
        }
    }

    /**
     * 测试证书
     */
    testCertificate(certName) {
        console.log('\n🧪 测试证书...');
        
        try {
            // 创建测试文件
            const testFile = path.join(this.projectRoot, 'test-sign.txt');
            fs.writeFileSync(testFile, 'Test file for codesigning');
            
            // 尝试签名
            execSync(`codesign --force --sign "${certName}" "${testFile}"`, { stdio: 'pipe' });
            
            // 验证签名
            execSync(`codesign --verify --verbose "${testFile}"`, { stdio: 'pipe' });
            
            // 清理测试文件
            fs.unlinkSync(testFile);
            
            console.log('✅ 证书测试通过');
            return true;
        } catch (error) {
            console.log('❌ 证书测试失败');
            return false;
        }
    }

    /**
     * 主流程
     */
    async run() {
        try {
            console.log('🍎 Apple Developer ID 证书申请助手\n');
            
            // 检查环境
            if (!this.checkEnvironment()) {
                process.exit(1);
            }
            
            // 检查现有证书
            const certificates = this.checkExistingCertificates();
            
            if (certificates.length > 0) {
                // 有证书，选择使用
                const selectedCert = await this.selectCertificate(certificates);
                
                if (selectedCert) {
                    this.saveCertificateConfig(selectedCert);
                    
                    if (this.testCertificate(selectedCert)) {
                        console.log('\n🎉 证书配置完成！');
                        console.log('💡 现在可以运行: npm run build:signed');
                    }
                } else {
                    console.log('❌ 未选择有效证书');
                }
            } else {
                // 没有证书，指导申请
                console.log('\n⚠️  未找到 Developer ID 证书');
                
                const needCSR = await this.askQuestion('是否需要生成 CSR 文件？(y/n): ');
                
                if (needCSR.toLowerCase() === 'y') {
                    await this.generateCSR();
                }
                
                this.showApplicationGuide();
                await this.openWebsites();
                
                console.log('\n💡 完成证书申请后，请重新运行此工具:');
                console.log('   npm run setup:codesign');
            }
            
        } catch (error) {
            console.error('\n❌ 操作失败:', error.message);
        } finally {
            this.rl.close();
        }
    }
}

// 运行助手
if (require.main === module) {
    const helper = new CertificateHelper();
    helper.run();
}

module.exports = CertificateHelper;
