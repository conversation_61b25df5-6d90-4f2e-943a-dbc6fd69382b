#!/usr/bin/env node

/**
 * 重新打包DMG脚本 - 修复标题和布局问题
 * 
 * 此脚本用于重新打包现有的DMG文件，修复以下问题：
 * 1. DMG标题与文件名保持一致
 * 2. 调整图标位置，确保教程图片完全可见
 * 3. 将app和Applications移到上方
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// 配置
const config = {
    projectRoot: path.join(__dirname, '..'),
    productName: '小梅花AI智能客服',
    distDir: path.join(__dirname, '..', 'dist'),
    tempDir: path.join(__dirname, '..', 'temp-rebuild'),
    buildDir: path.join(__dirname, '..', 'build')
};

// 日志函数
function log(message, type = 'info') {
    const colors = {
        info: '\x1b[36m',
        success: '\x1b[32m',
        warning: '\x1b[33m',
        error: '\x1b[31m',
        step: '\x1b[35m',
        reset: '\x1b[0m'
    };
    
    const icons = {
        info: 'ℹ️',
        success: '✅',
        warning: '⚠️',
        error: '❌',
        step: '🔧'
    };
    
    console.log(`${colors[type]}${icons[type]} ${message}${colors.reset}`);
}

// 执行命令
function exec(command, options = {}) {
    try {
        if (!options.silent) {
            log(`执行: ${command}`, 'info');
        }
        return execSync(command, { 
            encoding: 'utf8', 
            stdio: options.silent ? 'pipe' : 'inherit',
            cwd: config.projectRoot,
            ...options 
        });
    } catch (error) {
        throw new Error(`命令执行失败: ${command}\n${error.message}`);
    }
}

// 获取版本号
function getVersion() {
    const packageJsonPath = path.join(config.projectRoot, 'package.json');
    const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
    return packageJson.version;
}

// 清理临时目录
function cleanTempDir() {
    if (fs.existsSync(config.tempDir)) {
        exec(`rm -rf "${config.tempDir}"`);
    }
    fs.mkdirSync(config.tempDir, { recursive: true });
}

// 查找现有DMG文件
function findExistingDMGs() {
    if (!fs.existsSync(config.distDir)) {
        throw new Error('dist目录不存在，请先构建DMG文件');
    }

    const files = fs.readdirSync(config.distDir);
    const dmgFiles = files.filter(file => 
        file.endsWith('.dmg') && 
        file.includes(config.productName)
    );

    if (dmgFiles.length === 0) {
        throw new Error('未找到现有的DMG文件，请先构建DMG文件');
    }

    return dmgFiles.map(file => path.join(config.distDir, file));
}

// 重新打包单个DMG文件
function rebuildDMG(dmgPath) {
    const dmgFileName = path.basename(dmgPath);
    log(`重新打包: ${dmgFileName}`, 'step');

    // 确定架构类型
    let arch = '';
    let archName = '';
    if (dmgFileName.includes('arm64') || dmgFileName.includes('M芯片')) {
        arch = 'arm64';
        archName = 'M芯片版本';
    } else if (dmgFileName.includes('x64') || dmgFileName.includes('Intel')) {
        arch = 'x64';
        archName = 'Intel版本';
    } else {
        throw new Error(`无法确定DMG文件的架构类型: ${dmgFileName}`);
    }

    const version = getVersion();
    const newDMGTitle = `${config.productName}-${version}-${archName}`;
    const newDMGFileName = `${config.productName}-${version}-${archName}.dmg`;

    // 创建临时工作目录
    const workDir = path.join(config.tempDir, `rebuild-${arch}`);
    fs.mkdirSync(workDir, { recursive: true });

    try {
        // 挂载现有DMG
        log(`挂载现有DMG: ${dmgFileName}`, 'info');
        const mountResult = exec(`hdiutil attach "${dmgPath}" -readonly -nobrowse`, { silent: true });
        const mountLines = mountResult.split('\n').filter(line => line.includes('/Volumes/'));
        if (mountLines.length === 0) {
            throw new Error('无法挂载DMG文件');
        }
        const mountPoint = mountLines[0].split('\t').pop().trim();

        try {
            // 复制内容到工作目录
            log('复制DMG内容...', 'info');
            exec(`cp -R "${mountPoint}"/* "${workDir}"/`);

            // 卸载DMG
            exec(`hdiutil detach "${mountPoint}"`, { silent: true });

            // 复制教程图片到工作目录
            const tutorialImageSrc = path.join(config.buildDir, 'Mac电脑安装教程.png');
            const tutorialImageDst = path.join(workDir, 'Mac电脑安装教程.png');
            if (fs.existsSync(tutorialImageSrc)) {
                exec(`cp "${tutorialImageSrc}" "${tutorialImageDst}"`);
                log('已添加教程图片', 'info');
            }

            // 创建新的DMG
            log('创建优化的DMG...', 'info');
            const tempDMGPath = path.join(config.tempDir, `temp-${arch}.dmg`);
            exec(`hdiutil create -srcfolder "${workDir}" -format UDRW -volname "${newDMGTitle}" "${tempDMGPath}"`);

            // 挂载新DMG进行布局设置
            log('设置DMG布局...', 'info');
            const newMountResult = exec(`hdiutil attach "${tempDMGPath}" -readwrite -nobrowse`, { silent: true });
            const newMountLines = newMountResult.split('\n').filter(line => line.includes('/Volumes/'));
            const newMountPoint = newMountLines[0].split('\t').pop().trim();

            try {
                // 设置DMG窗口属性和图标位置
                const appleScript = `
tell application "Finder"
    tell disk "${newDMGTitle}"
        open
        set current view of container window to icon view
        set toolbar visible of container window to false
        set statusbar visible of container window to false
        set the bounds of container window to {400, 200, 1040, 680}
        set viewOptions to the icon view options of container window
        set arrangement of viewOptions to not arranged
        set icon size of viewOptions to 100
        delay 1
        
        -- 设置应用程序图标位置 (上方左侧)
        set position of item "${config.productName}.app" to {160, 120}
        
        -- 设置Applications文件夹位置 (上方右侧)
        set position of item "Applications" to {480, 120}
        
        -- 设置教程图片位置 (中下方)
        if exists item "Mac电脑安装教程.png" then
            set position of item "Mac电脑安装教程.png" to {320, 300}
        end if
        
        delay 1
        update without registering applications
        delay 2
        close
    end tell
end tell
`;

                // 执行AppleScript
                const scriptPath = path.join(config.tempDir, `layout-${arch}.scpt`);
                fs.writeFileSync(scriptPath, appleScript);
                exec(`osascript "${scriptPath}"`);

                // 卸载DMG
                exec(`hdiutil detach "${newMountPoint}"`, { silent: true });

                // 转换为只读DMG
                const finalDMGPath = path.join(config.distDir, newDMGFileName);
                if (fs.existsSync(finalDMGPath)) {
                    fs.unlinkSync(finalDMGPath);
                }
                exec(`hdiutil convert "${tempDMGPath}" -format UDZO -o "${finalDMGPath}"`);

                log(`重新打包完成: ${newDMGFileName}`, 'success');
                return finalDMGPath;

            } catch (error) {
                // 确保卸载DMG
                try {
                    exec(`hdiutil detach "${newMountPoint}"`, { silent: true });
                } catch (e) {
                    // 忽略卸载错误
                }
                throw error;
            }

        } catch (error) {
            // 确保卸载DMG
            try {
                exec(`hdiutil detach "${mountPoint}"`, { silent: true });
            } catch (e) {
                // 忽略卸载错误
            }
            throw error;
        }

    } finally {
        // 清理工作目录
        if (fs.existsSync(workDir)) {
            exec(`rm -rf "${workDir}"`);
        }
    }
}

// 显示结果
function showResults(rebuiltFiles) {
    const version = getVersion();

    console.log('\n🎉 DMG重新打包完成！\n');
    console.log('📦 优化的文件:');

    rebuiltFiles.forEach(filePath => {
        const fileName = path.basename(filePath);
        const stats = fs.statSync(filePath);
        const sizeInMB = (stats.size / 1024 / 1024).toFixed(1);

        // 判断架构类型
        let archType = '';
        if (fileName.includes('M芯片版本')) {
            archType = ' (M芯片版本)';
        } else if (fileName.includes('Intel版本')) {
            archType = ' (Intel版本)';
        }

        console.log(`  ✅ ${fileName}${archType} (${sizeInMB}MB)`);
    });

    console.log(`\n📁 文件位置: ${config.distDir}`);
    console.log('\n✨ 优化内容:');
    console.log('  ✅ DMG标题现在与文件名保持一致');
    console.log('  ✅ App和Applications图标移到上方 (Y:120)');
    console.log('  ✅ 教程图片移到中下方 (Y:300) - 完全可见');
    console.log('  ✅ 窗口大小: 640x480像素');
    console.log('  ✅ 窗口位置: X:400 Y:200');
}

// 主函数
async function rebuildDMGs() {
    try {
        console.log('🔧 开始重新打包DMG文件...\n');
        
        // 检查环境
        if (process.platform !== 'darwin') {
            throw new Error('此脚本只能在macOS系统上运行');
        }

        const version = getVersion();
        log(`当前版本: ${version}`, 'info');

        // 清理临时目录
        log('准备临时目录...', 'step');
        cleanTempDir();

        // 查找现有DMG文件
        log('查找现有DMG文件...', 'step');
        const existingDMGs = findExistingDMGs();
        log(`找到 ${existingDMGs.length} 个DMG文件`, 'info');

        // 重新打包每个DMG文件
        const rebuiltFiles = [];
        for (const dmgPath of existingDMGs) {
            const rebuiltPath = rebuildDMG(dmgPath);
            rebuiltFiles.push(rebuiltPath);
        }

        // 清理临时目录
        log('清理临时文件...', 'step');
        if (fs.existsSync(config.tempDir)) {
            exec(`rm -rf "${config.tempDir}"`);
        }

        // 显示结果
        showResults(rebuiltFiles);

        console.log('\n🎉 重新打包完成！');

    } catch (error) {
        log(`重新打包失败: ${error.message}`, 'error');
        
        // 清理临时目录
        if (fs.existsSync(config.tempDir)) {
            exec(`rm -rf "${config.tempDir}"`);
        }
        
        process.exit(1);
    }
}

// 显示帮助信息
function showHelp() {
    console.log(`
🔧 DMG重新打包工具 - 修复标题和布局

用法:
  node scripts/rebuild-dmg-fixed.js

功能:
  ✅ 修复DMG标题与文件名不一致的问题
  ✅ 将App和Applications图标移到上方
  ✅ 将教程图片移到中下方，确保完全可见
  ✅ 保持原有的文件内容和签名

注意:
  - 只能在macOS系统上运行
  - 需要先有现有的DMG文件
  - 会覆盖原有的DMG文件
`);
}

// 检查命令行参数
if (process.argv.includes('--help') || process.argv.includes('-h')) {
    showHelp();
    process.exit(0);
}

// 运行重新打包
rebuildDMGs();
