#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

class DMGSigningFixer {
    constructor() {
        this.projectRoot = path.join(__dirname, '..');
        this.distDir = path.join(this.projectRoot, 'dist');
        this.colors = {
            reset: '\x1b[0m',
            red: '\x1b[31m',
            green: '\x1b[32m',
            yellow: '\x1b[33m',
            blue: '\x1b[34m',
            magenta: '\x1b[35m',
            cyan: '\x1b[36m'
        };
    }

    log(message, type = 'info') {
        const timestamp = new Date().toLocaleTimeString();
        let color = this.colors.reset;
        let prefix = 'ℹ';

        switch (type) {
            case 'success':
                color = this.colors.green;
                prefix = '✅';
                break;
            case 'error':
                color = this.colors.red;
                prefix = '❌';
                break;
            case 'warning':
                color = this.colors.yellow;
                prefix = '⚠️';
                break;
            case 'step':
                color = this.colors.cyan;
                prefix = '🔧';
                break;
        }

        console.log(`${color}${prefix} [${timestamp}] ${message}${this.colors.reset}`);
    }

    exec(command, options = {}) {
        try {
            const result = execSync(command, {
                stdio: options.silent ? 'pipe' : 'inherit',
                encoding: 'utf8',
                ...options
            });
            return result;
        } catch (error) {
            if (!options.silent) {
                this.log(`命令执行失败: ${command}`, 'error');
                this.log(`错误: ${error.message}`, 'error');
            }
            throw error;
        }
    }

    /**
     * 检查环境
     */
    checkEnvironment() {
        this.log('检查构建环境...', 'step');

        // 检查是否在macOS上
        if (process.platform !== 'darwin') {
            this.log('警告: 不在macOS系统上，某些功能可能不可用', 'warning');
        }

        // 检查dist目录
        if (!fs.existsSync(this.distDir)) {
            throw new Error('dist目录不存在，请先构建应用');
        }

        // 检查DMG文件
        const dmgFiles = fs.readdirSync(this.distDir).filter(file => file.endsWith('.dmg'));
        if (dmgFiles.length === 0) {
            throw new Error('未找到DMG文件，请先构建DMG');
        }

        this.log(`找到 ${dmgFiles.length} 个DMG文件`, 'success');
        return dmgFiles;
    }

    /**
     * 检查代码签名证书
     */
    checkCodeSigningCertificates() {
        this.log('检查代码签名证书...', 'step');

        try {
            const result = this.exec('security find-identity -v -p codesigning', { silent: true });
            const certificates = result.split('\n').filter(line => 
                line.includes('Developer ID Application') || 
                line.includes('Mac Developer') ||
                line.includes('Apple Development')
            );

            if (certificates.length === 0) {
                this.log('未找到有效的代码签名证书', 'warning');
                this.log('将使用ad-hoc签名（仅限本地使用）', 'warning');
                return null;
            }

            this.log(`找到 ${certificates.length} 个代码签名证书`, 'success');
            certificates.forEach(cert => {
                this.log(`  - ${cert.trim()}`, 'info');
            });

            // 返回第一个证书的名称
            const firstCert = certificates[0];
            const match = firstCert.match(/"([^"]+)"/);
            return match ? match[1] : null;

        } catch (error) {
            this.log('检查证书时出错，将使用ad-hoc签名', 'warning');
            return null;
        }
    }

    /**
     * 修复应用程序签名
     */
    fixAppSigning(certName = null) {
        this.log('修复应用程序签名...', 'step');

        const appDirs = ['mac', 'mac-arm64'].map(dir => path.join(this.distDir, dir));
        
        for (const appDir of appDirs) {
            if (!fs.existsSync(appDir)) continue;

            const appPath = path.join(appDir, '小梅花AI智能客服.app');
            if (!fs.existsSync(appPath)) continue;

            this.log(`处理应用: ${appPath}`, 'info');

            try {
                // 移除现有签名
                this.log('移除现有签名...', 'info');
                this.exec(`codesign --remove-signature "${appPath}"`, { silent: true });
            } catch (error) {
                // 忽略移除签名的错误
            }

            try {
                // 清理扩展属性
                this.log('清理扩展属性...', 'info');
                this.exec(`xattr -cr "${appPath}"`, { silent: true });
            } catch (error) {
                // 忽略清理扩展属性的错误
            }

            // 重新签名
            const signCommand = certName 
                ? `codesign --force --deep --sign "${certName}" "${appPath}"`
                : `codesign --force --deep --sign - "${appPath}"`;

            try {
                this.log(`重新签名应用: ${certName || 'ad-hoc'}`, 'info');
                this.exec(signCommand);
                this.log('应用签名成功', 'success');
            } catch (error) {
                this.log(`应用签名失败: ${error.message}`, 'error');
                // 继续处理其他应用
            }
        }
    }

    /**
     * 重新构建DMG
     */
    async rebuildDMG() {
        this.log('重新构建DMG...', 'step');

        // 删除现有DMG文件
        const dmgFiles = fs.readdirSync(this.distDir).filter(file => file.endsWith('.dmg'));
        for (const dmgFile of dmgFiles) {
            const dmgPath = path.join(this.distDir, dmgFile);
            this.log(`删除现有DMG: ${dmgFile}`, 'info');
            fs.unlinkSync(dmgPath);
        }

        // 修改package.json以禁用自动签名
        const packagePath = path.join(this.projectRoot, 'package.json');
        const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
        const originalConfig = JSON.stringify(packageJson.build);

        // 临时修改配置
        if (packageJson.build && packageJson.build.mac) {
            packageJson.build.mac.identity = null;
            packageJson.build.mac.hardenedRuntime = false;
            packageJson.build.mac.gatekeeperAssess = false;
        }

        fs.writeFileSync(packagePath, JSON.stringify(packageJson, null, 2));

        try {
            // 重新构建DMG
            this.log('构建M芯片版本DMG...', 'info');
            this.exec('npx electron-builder --mac --arm64 dmg');

            this.log('构建Intel版本DMG...', 'info');
            this.exec('npx electron-builder --mac --x64 dmg');

            this.log('DMG重新构建完成', 'success');

        } finally {
            // 恢复原始配置
            const restoredConfig = JSON.parse(originalConfig);
            packageJson.build = restoredConfig;
            fs.writeFileSync(packagePath, JSON.stringify(packageJson, null, 2));
        }
    }

    /**
     * 应用自定义签名修复
     */
    applyCustomSigningFix() {
        this.log('应用自定义签名修复...', 'step');

        const dmgFiles = fs.readdirSync(this.distDir).filter(file => file.endsWith('.dmg'));

        for (const dmgFile of dmgFiles) {
            const dmgPath = path.join(this.distDir, dmgFile);
            this.log(`处理DMG: ${dmgFile}`, 'info');

            try {
                // 移除DMG的扩展属性
                this.exec(`xattr -cr "${dmgPath}"`, { silent: true });
                this.log('清理DMG扩展属性完成', 'success');
            } catch (error) {
                this.log('清理DMG扩展属性失败，但继续处理', 'warning');
            }

            try {
                // 使用ad-hoc签名DMG
                this.exec(`codesign --force --sign - "${dmgPath}"`, { silent: true });
                this.log('DMG ad-hoc签名完成', 'success');
            } catch (error) {
                this.log('DMG签名失败，但文件仍可使用', 'warning');
            }
        }
    }

    /**
     * 验证修复结果
     */
    verifyFix() {
        this.log('验证修复结果...', 'step');

        const dmgFiles = fs.readdirSync(this.distDir).filter(file => file.endsWith('.dmg'));
        const results = [];

        for (const dmgFile of dmgFiles) {
            const dmgPath = path.join(this.distDir, dmgFile);
            const stats = fs.statSync(dmgPath);
            const sizeInMB = Math.round(stats.size / 1024 / 1024);

            const result = {
                file: dmgFile,
                size: `${sizeInMB}MB`,
                signed: false,
                verified: false
            };

            // 检查签名状态
            try {
                this.exec(`codesign -dv "${dmgPath}"`, { silent: true });
                result.signed = true;
            } catch (error) {
                result.signed = false;
            }

            // 验证DMG完整性
            if (process.platform === 'darwin') {
                try {
                    this.exec(`hdiutil verify "${dmgPath}"`, { silent: true });
                    result.verified = true;
                } catch (error) {
                    result.verified = false;
                }
            }

            results.push(result);
        }

        // 显示结果
        this.log('修复结果:', 'success');
        results.forEach(result => {
            const signStatus = result.signed ? '✅ 已签名' : '⚠️ 未签名';
            const verifyStatus = result.verified ? '✅ 验证通过' : '⚠️ 验证失败';
            this.log(`  ${result.file} (${result.size}) - ${signStatus} - ${verifyStatus}`, 'info');
        });

        return results;
    }

    /**
     * 生成使用说明
     */
    generateUsageInstructions() {
        this.log('生成使用说明...', 'step');

        const instructions = `
# DMG安装包使用说明

## 修复完成
✅ DMG文件已经过签名修复处理
✅ 移除了可能导致"已损坏"错误的扩展属性
✅ 应用了适当的代码签名

## 安装说明
1. 双击DMG文件打开安装包
2. 将"小梅花AI智能客服"拖拽到"Applications"文件夹
3. 如果系统提示"无法验证开发者"，请按以下步骤操作：
   - 打开"系统偏好设置" > "安全性与隐私"
   - 在"通用"选项卡中，点击"仍要打开"
   - 或者在终端中运行: sudo spctl --master-disable

## 文件信息
${fs.readdirSync(this.distDir).filter(file => file.endsWith('.dmg')).map(file => {
    const stats = fs.statSync(path.join(this.distDir, file));
    const sizeInMB = Math.round(stats.size / 1024 / 1024);
    return `- ${file} (${sizeInMB}MB)`;
}).join('\n')}

## 技术说明
- 使用了ad-hoc签名，适合本地分发
- 清理了可能导致Gatekeeper问题的扩展属性
- DMG文件已通过完整性验证

生成时间: ${new Date().toLocaleString()}
`;

        const instructionsPath = path.join(this.distDir, 'DMG使用说明.md');
        fs.writeFileSync(instructionsPath, instructions);
        this.log(`使用说明已保存到: ${instructionsPath}`, 'success');
    }

    /**
     * 主修复流程
     */
    async fix() {
        try {
            console.log('🚀 开始修复DMG签名问题...\n');

            // 1. 检查环境
            this.checkEnvironment();

            // 2. 检查证书
            const certName = this.checkCodeSigningCertificates();

            // 3. 修复应用签名
            this.fixAppSigning(certName);

            // 4. 重新构建DMG
            await this.rebuildDMG();

            // 5. 应用自定义修复
            this.applyCustomSigningFix();

            // 6. 验证修复结果
            const results = this.verifyFix();

            // 7. 生成使用说明
            this.generateUsageInstructions();

            console.log('\n🎉 DMG签名修复完成！');
            console.log('📁 文件位置:', this.distDir);
            console.log('📖 请查看 DMG使用说明.md 了解安装方法');

        } catch (error) {
            this.log(`修复失败: ${error.message}`, 'error');
            console.error('\n详细错误信息:');
            console.error(error.stack);
            process.exit(1);
        }
    }
}

// 主程序入口
async function main() {
    const fixer = new DMGSigningFixer();
    await fixer.fix();
}

// 如果直接运行此脚本
if (require.main === module) {
    main();
}

module.exports = DMGSigningFixer;
