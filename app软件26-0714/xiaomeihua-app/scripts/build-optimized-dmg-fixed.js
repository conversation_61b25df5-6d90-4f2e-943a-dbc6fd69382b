#!/usr/bin/env node

/**
 * 优化的DMG打包脚本 - 修复标题和布局问题
 * 
 * 修复内容：
 * 1. DMG标题与文件名保持一致
 * 2. 调整图标位置，确保教程图片完全可见
 * 3. 将app和Applications移到上方
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// 配置
const config = {
    projectRoot: path.join(__dirname, '..'),
    productName: '小梅花AI智能客服',
    distDir: path.join(__dirname, '..', 'dist'),
    // 只构建这两个版本
    targets: [
        { arch: 'arm64', name: 'M芯片版本', title: 'M芯片版本' },
        { arch: 'x64', name: 'Intel版本', title: 'Intel版本' }
    ]
};

// 日志函数
function log(message, type = 'info') {
    const colors = {
        info: '\x1b[36m',
        success: '\x1b[32m',
        warning: '\x1b[33m',
        error: '\x1b[31m',
        step: '\x1b[35m',
        reset: '\x1b[0m'
    };
    
    const icons = {
        info: 'ℹ️',
        success: '✅',
        warning: '⚠️',
        error: '❌',
        step: '🔧'
    };
    
    console.log(`${colors[type]}${icons[type]} ${message}${colors.reset}`);
}

// 执行命令
function exec(command, options = {}) {
    try {
        log(`执行: ${command}`, 'info');
        return execSync(command, { 
            encoding: 'utf8', 
            stdio: 'inherit',
            cwd: config.projectRoot,
            ...options 
        });
    } catch (error) {
        throw new Error(`命令执行失败: ${command}\n${error.message}`);
    }
}

// 获取版本号
function getVersion() {
    const packageJsonPath = path.join(config.projectRoot, 'package.json');
    const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
    return packageJson.version;
}

// 清理旧文件
function cleanOldFiles() {
    log('清理旧的DMG文件...', 'step');
    
    if (fs.existsSync(config.distDir)) {
        const files = fs.readdirSync(config.distDir);
        const dmgFiles = files.filter(file => file.endsWith('.dmg'));
        
        dmgFiles.forEach(file => {
            const filePath = path.join(config.distDir, file);
            fs.unlinkSync(filePath);
            log(`删除旧文件: ${file}`, 'info');
        });
    }
    
    log('清理完成', 'success');
}

// 备份和修改package.json
function modifyPackageJson(arch, version) {
    const packageJsonPath = path.join(config.projectRoot, 'package.json');
    const backupPath = path.join(config.projectRoot, 'package.json.backup');

    // 备份原始文件
    const originalContent = fs.readFileSync(packageJsonPath, 'utf8');
    fs.writeFileSync(backupPath, originalContent);

    // 解析配置
    const packageJson = JSON.parse(originalContent);

    // 根据架构设置DMG标题
    let dmgTitle = '';
    if (arch === 'arm64') {
        dmgTitle = `${config.productName}-${version}-M芯片版本`;
    } else if (arch === 'x64') {
        dmgTitle = `${config.productName}-${version}-Intel版本`;
    }

    // 修改DMG配置
    packageJson.build.dmg.title = dmgTitle;
    packageJson.build.dmg.contents = [
        {
            "x": 160,
            "y": 120,
            "type": "file"
        },
        {
            "x": 480,
            "y": 120,
            "type": "link",
            "path": "/Applications"
        },
        {
            "x": 320,
            "y": 300,
            "type": "file",
            "path": "build/Mac电脑安装教程.png"
        }
    ];

    // 保存修改后的配置
    fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2));

    return backupPath;
}

// 恢复package.json
function restorePackageJson(backupPath) {
    const packageJsonPath = path.join(config.projectRoot, 'package.json');
    if (fs.existsSync(backupPath)) {
        fs.copyFileSync(backupPath, packageJsonPath);
        fs.unlinkSync(backupPath);
    }
}

// 构建单个架构
function buildArch(target) {
    log(`开始构建 ${target.arch} 架构 (${target.name})...`, 'step');

    const version = getVersion();
    let backupPath = null;

    try {
        // 修改package.json配置
        backupPath = modifyPackageJson(target.arch, version);

        // 构建
        const buildCommand = `npx electron-builder --mac --${target.arch}`;
        exec(buildCommand);

        log(`${target.arch} 架构构建完成`, 'success');
    } finally {
        // 恢复原始配置
        if (backupPath) {
            restorePackageJson(backupPath);
        }
    }
}

// 重命名DMG文件
function renameDMGFiles() {
    log('重命名DMG文件...', 'step');

    if (!fs.existsSync(config.distDir)) {
        throw new Error('dist目录不存在');
    }

    const version = getVersion();
    const files = fs.readdirSync(config.distDir);
    const dmgFiles = files.filter(file => file.endsWith('.dmg'));

    if (dmgFiles.length === 0) {
        throw new Error('未找到DMG文件');
    }

    const renamedFiles = [];

    dmgFiles.forEach(file => {
        const oldPath = path.join(config.distDir, file);
        let newFileName = '';

        if (file.includes('arm64')) {
            newFileName = `小梅花AI智能客服-${version}-M芯片版本.dmg`;
        } else if (file.includes('x64')) {
            newFileName = `小梅花AI智能客服-${version}-Intel版本.dmg`;
        } else {
            // 保持原文件名
            newFileName = file;
        }

        const newPath = path.join(config.distDir, newFileName);

        if (oldPath !== newPath) {
            fs.renameSync(oldPath, newPath);
            log(`重命名: ${file} -> ${newFileName}`, 'info');
        }

        renamedFiles.push(newFileName);
    });

    log('DMG文件重命名完成', 'success');
    return renamedFiles;
}

// 检查构建结果
function checkBuildResults() {
    log('检查构建结果...', 'step');

    if (!fs.existsSync(config.distDir)) {
        throw new Error('dist目录不存在');
    }

    const files = fs.readdirSync(config.distDir);
    const dmgFiles = files.filter(file => file.endsWith('.dmg'));

    if (dmgFiles.length === 0) {
        throw new Error('未找到DMG文件');
    }

    log('构建结果检查完成', 'success');
    return dmgFiles;
}

// 显示构建结果
function showResults(dmgFiles) {
    const version = getVersion();

    console.log('\n🎉 优化DMG打包完成！\n');
    console.log('📦 生成的文件:');

    dmgFiles.forEach(file => {
        const filePath = path.join(config.distDir, file);
        const stats = fs.statSync(filePath);
        const sizeInMB = (stats.size / 1024 / 1024).toFixed(1);

        // 判断架构类型
        let archType = '';
        if (file.includes('M芯片版本')) {
            archType = ' (M芯片版本)';
        } else if (file.includes('Intel版本')) {
            archType = ' (Intel版本)';
        } else if (file.includes('arm64')) {
            archType = ' (M芯片版本)';
        } else if (file.includes('x64')) {
            archType = ' (Intel版本)';
        }

        console.log(`  ✅ ${file}${archType} (${sizeInMB}MB)`);
    });

    console.log(`\n📁 文件位置: ${config.distDir}`);
    console.log('\n📋 优化配置:');
    console.log('  ✅ DMG标题与文件名保持一致');
    console.log('  ✅ 窗口大小: 640x480像素');
    console.log('  ✅ 窗口位置: X:400 Y:200');
    console.log('  ✅ App和Applications位于上方 (Y:120)');
    console.log('  ✅ 教程图片位于中下方 (Y:300) - 完全可见');
    console.log('  ✅ 只包含: M芯片版本 + Intel版本');

    // 验证DMG文件（仅macOS）
    if (process.platform === 'darwin') {
        console.log('\n🔍 验证DMG文件...');
        dmgFiles.forEach(file => {
            try {
                exec(`hdiutil verify "${path.join(config.distDir, file)}"`, { stdio: 'pipe' });
                log(`${file} 验证通过`, 'success');
            } catch (error) {
                log(`${file} 验证失败，但可能仍然可用`, 'warning');
            }
        });
    }
}

// 主函数
async function buildOptimizedDMG() {
    try {
        console.log('🚀 开始打包优化的M版本和Intel版本DMG...\n');
        
        // 1. 检查环境
        log('检查构建环境...', 'step');
        if (process.platform !== 'darwin') {
            log('警告: 不在macOS系统上运行，可能影响构建质量', 'warning');
        }
        
        const version = getVersion();
        log(`当前版本: ${version}`, 'info');
        
        // 2. 安装依赖
        log('检查并安装依赖...', 'step');
        exec('npm install');
        
        // 3. 清理旧文件
        cleanOldFiles();
        
        // 4. 构建两个版本
        for (const target of config.targets) {
            buildArch(target);
        }

        // 5. 检查结果
        checkBuildResults();

        // 6. 重命名DMG文件
        const renamedFiles = renameDMGFiles();

        // 7. 显示结果
        showResults(renamedFiles);
        
        console.log('\n🎉 优化打包完成！');
        console.log('\n✨ 修复内容:');
        console.log('  1. ✅ DMG标题现在与文件名保持一致');
        console.log('  2. ✅ App和Applications图标移到上方');
        console.log('  3. ✅ 教程图片向上移动，确保完全可见');
        
    } catch (error) {
        log(`构建失败: ${error.message}`, 'error');
        console.log('\n💡 提示:');
        console.log('  - 确保已安装Xcode命令行工具');
        console.log('  - 确保网络连接正常');
        console.log('  - 检查package.json配置是否正确');
        process.exit(1);
    }
}

// 显示帮助信息
function showHelp() {
    console.log(`
🔨 优化的M版本和Intel版本DMG打包工具

用法:
  node scripts/build-optimized-dmg-fixed.js

修复内容:
  ✅ DMG标题与文件名保持一致
  ✅ App和Applications图标移到上方 (Y:120)
  ✅ 教程图片向上移动到Y:300，确保完全可见
  ✅ 窗口大小：640x480像素
  ✅ 窗口位置：X:400 Y:200
  ✅ 文件命名：小梅花AI智能客服-xxxx-M芯片版本.dmg 和 小梅花AI智能客服-xxxx-Intel版本.dmg

注意:
  - 建议在macOS系统上运行以获得最佳体验
  - 确保已安装Xcode命令行工具
  - 首次运行会自动安装依赖
`);
}

// 检查命令行参数
if (process.argv.includes('--help') || process.argv.includes('-h')) {
    showHelp();
    process.exit(0);
}

// 运行构建
buildOptimizedDMG();
