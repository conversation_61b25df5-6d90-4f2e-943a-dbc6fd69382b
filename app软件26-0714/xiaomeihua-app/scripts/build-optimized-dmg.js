#!/usr/bin/env node

/**
 * 优化的DMG构建脚本
 * 专门解决"已损坏"问题，确保应用程序显示"无法验证"而不是"已损坏"
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

class OptimizedDMGBuilder {
    constructor() {
        this.projectRoot = path.resolve(__dirname, '..');
        this.distDir = path.join(this.projectRoot, 'dist');
        this.buildDir = path.join(this.projectRoot, 'build');
        this.tempDir = path.join(this.projectRoot, 'temp-build');
        
        // 解析命令行参数
        const args = process.argv.slice(2);
        this.targetArch = args[0] || 'universal'; // x64, arm64, universal
        
        this.packageJson = require(path.join(this.projectRoot, 'package.json'));
        this.productName = this.packageJson.productName;
        this.version = this.packageJson.version;
        
        this.log('🚀 优化的DMG构建工具');
        this.log(`目标架构: ${this.targetArch}`);
        this.log(`产品名称: ${this.productName}`);
        this.log(`版本: ${this.version}`);
    }

    /**
     * 彩色日志输出
     */
    log(message, type = 'info') {
        const colors = {
            info: '\x1b[36m',
            success: '\x1b[32m',
            warning: '\x1b[33m',
            error: '\x1b[31m',
            step: '\x1b[35m',
            reset: '\x1b[0m'
        };
        
        const icons = {
            info: 'ℹ️',
            success: '✅',
            warning: '⚠️',
            error: '❌',
            step: '🔄'
        };
        
        const timestamp = new Date().toLocaleTimeString('zh-CN');
        console.log(`${colors[type]}[${timestamp}] ${icons[type]} ${message}${colors.reset}`);
    }

    /**
     * 执行命令
     */
    exec(command, options = {}) {
        try {
            this.log(`执行命令: ${command}`, 'info');
            const result = execSync(command, {
                encoding: 'utf8',
                stdio: options.silent ? 'pipe' : 'inherit',
                cwd: this.projectRoot,
                ...options
            });
            return result;
        } catch (error) {
            if (options.ignoreError) {
                this.log(`命令执行失败但忽略错误: ${error.message}`, 'warning');
                return null;
            }
            throw new Error(`命令执行失败: ${command}\n${error.message}`);
        }
    }

    /**
     * 步骤1: 环境检查和准备
     */
    prepareEnvironment() {
        this.log('准备构建环境...', 'step');
        
        // 检查操作系统
        if (process.platform !== 'darwin') {
            throw new Error('此脚本只能在macOS系统上运行');
        }
        
        // 清理旧的构建文件
        if (fs.existsSync(this.distDir)) {
            fs.rmSync(this.distDir, { recursive: true, force: true });
        }
        
        if (fs.existsSync(this.tempDir)) {
            fs.rmSync(this.tempDir, { recursive: true, force: true });
        }
        
        // 创建必要目录
        fs.mkdirSync(this.distDir, { recursive: true });
        fs.mkdirSync(this.tempDir, { recursive: true });
        
        this.log('环境准备完成', 'success');
    }

    /**
     * 步骤2: 构建应用程序（无签名）
     */
    buildApplication() {
        this.log('构建应用程序...', 'step');
        
        // 构建命令
        let buildCommand;
        switch (this.targetArch) {
            case 'x64':
                buildCommand = 'npx electron-builder --mac --x64 --config.mac.identity=null';
                break;
            case 'arm64':
                buildCommand = 'npx electron-builder --mac --arm64 --config.mac.identity=null';
                break;
            case 'universal':
                buildCommand = 'npx electron-builder --mac --universal --config.mac.identity=null';
                break;
            default:
                throw new Error(`不支持的架构: ${this.targetArch}`);
        }
        
        // 执行构建
        this.exec(buildCommand);
        
        this.log('应用程序构建完成', 'success');
    }

    /**
     * 步骤3: 提取并优化应用程序
     */
    extractAndOptimizeApp() {
        this.log('提取并优化应用程序...', 'step');
        
        // 查找构建的DMG文件
        const dmgFiles = fs.readdirSync(this.distDir).filter(file => 
            file.endsWith('.dmg') && file.includes(this.targetArch)
        );
        
        if (dmgFiles.length === 0) {
            throw new Error('未找到构建的DMG文件');
        }
        
        const dmgFile = dmgFiles[0];
        const dmgPath = path.join(this.distDir, dmgFile);
        
        this.log(`找到DMG文件: ${dmgFile}`);
        
        // 挂载DMG
        const mountResult = this.exec(`hdiutil attach "${dmgPath}" -readonly -nobrowse`, { silent: true });
        const mountLines = mountResult.split('\n').filter(line => line.includes('/Volumes/'));
        const mountPoint = mountLines[0].split('\t').pop().trim();
        
        try {
            // 复制应用程序
            const appName = `${this.productName}.app`;
            const sourceAppPath = path.join(mountPoint, appName);
            const tempAppPath = path.join(this.tempDir, appName);
            
            this.exec(`cp -R "${sourceAppPath}" "${tempAppPath}"`);
            this.log('应用程序已提取到临时目录', 'success');
            
            return tempAppPath;
            
        } finally {
            // 卸载DMG
            this.exec(`hdiutil detach "${mountPoint}"`, { ignoreError: true });
        }
    }

    /**
     * 步骤4: 优化应用程序签名
     */
    optimizeAppSigning(appPath) {
        this.log('优化应用程序签名...', 'step');
        
        // 移除现有签名和扩展属性
        this.exec(`codesign --remove-signature "${appPath}"`, { ignoreError: true });
        this.exec(`xattr -cr "${appPath}"`, { ignoreError: true });
        
        // 验证Info.plist
        const infoPlistPath = path.join(appPath, 'Contents', 'Info.plist');
        if (!fs.existsSync(infoPlistPath)) {
            throw new Error('Info.plist文件不存在');
        }
        
        this.exec(`plutil -lint "${infoPlistPath}"`, { silent: true });
        this.log('Info.plist验证通过', 'success');
        
        // 进行优化的adhoc签名
        this.log('执行优化的adhoc签名...');
        
        // 第一步：深度签名，确保资源封装
        this.exec(`codesign --force --deep --sign - --preserve-metadata=entitlements,requirements,flags,runtime "${appPath}"`);
        
        // 第二步：重新签名以确保Info.plist绑定
        this.exec(`codesign --force --sign - --preserve-metadata=entitlements "${appPath}"`);
        
        // 验证签名结果
        const signInfo = this.exec(`codesign -dv --verbose=4 "${appPath}" 2>&1`, { silent: true });
        
        if (signInfo.includes('Sealed Resources=none')) {
            this.log('警告: 资源封装可能仍然缺失', 'warning');
        } else {
            this.log('✅ 资源封装正常', 'success');
        }
        
        if (signInfo.includes('Info.plist=not bound')) {
            this.log('警告: Info.plist可能未正确绑定', 'warning');
        } else {
            this.log('✅ Info.plist绑定正常', 'success');
        }
        
        // 验证签名有效性
        this.exec(`codesign --verify --verbose "${appPath}"`);
        this.log('签名验证通过', 'success');
    }

    /**
     * 步骤5: 创建优化的DMG
     */
    createOptimizedDMG(appPath) {
        this.log('创建优化的DMG...', 'step');
        
        // 创建DMG内容目录
        const dmgContentDir = path.join(this.tempDir, 'dmg-content');
        fs.mkdirSync(dmgContentDir, { recursive: true });
        
        // 复制优化后的应用程序
        const appName = `${this.productName}.app`;
        const dmgAppPath = path.join(dmgContentDir, appName);
        this.exec(`cp -R "${appPath}" "${dmgAppPath}"`);
        
        // 复制资源文件
        this.copyDMGResources(dmgContentDir);
        
        // 生成优化的DMG文件名
        const optimizedDMGName = `${this.productName}-${this.version}-${this.targetArch}-optimized.dmg`;
        const optimizedDMGPath = path.join(this.distDir, optimizedDMGName);
        
        // 删除旧文件
        if (fs.existsSync(optimizedDMGPath)) {
            fs.unlinkSync(optimizedDMGPath);
        }
        
        // 创建DMG
        this.exec(`hdiutil create -srcfolder "${dmgContentDir}" -format UDZO -volname "${this.productName} ${this.version}" "${optimizedDMGPath}"`);
        
        this.log(`优化的DMG已创建: ${optimizedDMGPath}`, 'success');
        return optimizedDMGPath;
    }

    /**
     * 复制DMG资源文件
     */
    copyDMGResources(dmgDir) {
        this.log('复制DMG资源文件...');
        
        const resourcesDir = path.join(this.projectRoot, 'resources');
        
        // 复制修复脚本
        const fixScriptSrc = path.join(resourcesDir, '修复已损坏.command');
        if (fs.existsSync(fixScriptSrc)) {
            const fixScriptDest = path.join(dmgDir, '修复已损坏.command');
            fs.copyFileSync(fixScriptSrc, fixScriptDest);
            this.exec(`chmod +x "${fixScriptDest}"`);
        }
        
        // 复制安装说明
        const installGuideSrc = path.join(resourcesDir, '安装前先打开.txt');
        if (fs.existsSync(installGuideSrc)) {
            fs.copyFileSync(installGuideSrc, path.join(dmgDir, '安装前先打开.txt'));
        }
        
        // 创建Applications链接
        this.exec(`ln -s /Applications "${path.join(dmgDir, 'Applications')}"`);
        
        this.log('DMG资源文件复制完成', 'success');
    }

    /**
     * 步骤6: 验证优化结果
     */
    verifyOptimizedDMG(dmgPath) {
        this.log('验证优化结果...', 'step');
        
        // 验证DMG完整性
        this.exec(`hdiutil verify "${dmgPath}"`, { silent: true });
        this.log('DMG完整性验证通过', 'success');
        
        // 挂载并检查应用程序
        const mountResult = this.exec(`hdiutil attach "${dmgPath}" -readonly -nobrowse`, { silent: true });
        const mountLines = mountResult.split('\n').filter(line => line.includes('/Volumes/'));
        const mountPoint = mountLines[0].split('\t').pop().trim();
        
        try {
            const appName = `${this.productName}.app`;
            const mountedAppPath = path.join(mountPoint, appName);
            
            // 检查签名状态
            const signInfo = this.exec(`codesign -dv --verbose=4 "${mountedAppPath}" 2>&1`, { silent: true });
            
            let optimizationSuccess = true;
            
            if (signInfo.includes('Sealed Resources=none')) {
                this.log('⚠️  资源封装仍然缺失', 'warning');
                optimizationSuccess = false;
            } else {
                this.log('✅ 资源封装正常', 'success');
            }
            
            if (signInfo.includes('Info.plist=not bound')) {
                this.log('⚠️  Info.plist仍未绑定', 'warning');
                optimizationSuccess = false;
            } else {
                this.log('✅ Info.plist绑定正常', 'success');
            }
            
            return optimizationSuccess;
            
        } finally {
            this.exec(`hdiutil detach "${mountPoint}"`, { ignoreError: true });
        }
    }

    /**
     * 清理临时文件
     */
    cleanup() {
        this.log('清理临时文件...', 'step');

        if (fs.existsSync(this.tempDir)) {
            fs.rmSync(this.tempDir, { recursive: true, force: true });
            this.log('临时文件已清理', 'success');
        }
    }

    /**
     * 显示构建结果
     */
    showResults(dmgPath, optimizationSuccess) {
        console.log('\n' + '='.repeat(60));
        if (optimizationSuccess) {
            this.log('🎉 优化的DMG构建完成！', 'success');
        } else {
            this.log('⚠️  DMG构建完成，但优化可能不完全', 'warning');
        }
        console.log('='.repeat(60));

        console.log(`\n📋 构建信息:`);
        console.log(`  产品: ${this.productName}`);
        console.log(`  版本: ${this.version}`);
        console.log(`  架构: ${this.targetArch}`);
        console.log(`  输出文件: ${path.basename(dmgPath)}`);

        if (optimizationSuccess) {
            console.log(`\n✨ 优化效果:`);
            console.log(`  - 应用程序应该显示"无法验证"而不是"已损坏"`);
            console.log(`  - 包含资源封装和Info.plist绑定`);
            console.log(`  - 提供了修复脚本供用户使用`);
        } else {
            console.log(`\n⚠️  注意事项:`);
            console.log(`  - 优化可能不完全，建议使用修复脚本`);
            console.log(`  - 用户可能仍需要手动允许应用运行`);
        }

        console.log(`\n💡 用户安装指南:`);
        console.log(`  1. 双击打开DMG文件`);
        console.log(`  2. 将应用拖拽到Applications文件夹`);
        console.log(`  3. 如果出现"已损坏"提示，双击"修复已损坏.command"`);
        console.log(`  4. 或在系统偏好设置中允许应用运行`);

        console.log(`\n📁 文件位置: ${this.distDir}`);

        if (process.platform === 'darwin') {
            console.log(`\n🔍 快速查看:`);
            console.log(`  open "${this.distDir}"`);
        }
    }

    /**
     * 主构建流程
     */
    async build() {
        try {
            console.log('🚀 开始构建优化的DMG...\n');

            // 执行构建步骤
            this.prepareEnvironment();
            this.buildApplication();
            const appPath = this.extractAndOptimizeApp();
            this.optimizeAppSigning(appPath);
            const dmgPath = this.createOptimizedDMG(appPath);
            const optimizationSuccess = this.verifyOptimizedDMG(dmgPath);

            this.showResults(dmgPath, optimizationSuccess);

            return optimizationSuccess;

        } catch (error) {
            this.log(`构建失败: ${error.message}`, 'error');
            console.error('\n详细错误信息:');
            console.error(error.stack);
            return false;
        } finally {
            this.cleanup();
        }
    }
}

// 主程序入口
async function main() {
    const builder = new OptimizedDMGBuilder();
    const success = await builder.build();
    process.exit(success ? 0 : 1);
}

// 如果直接运行此脚本
if (require.main === module) {
    main();
}

module.exports = OptimizedDMGBuilder;
