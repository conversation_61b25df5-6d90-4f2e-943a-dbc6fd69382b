#!/bin/bash

# Apple Developer ID 代码签名设置脚本
# 用于检查和配置 macOS 应用代码签名环境

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目根目录
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
DOCS_DIR="$PROJECT_ROOT/docs"

echo -e "${BLUE}🍎 Apple Developer ID 代码签名设置助手${NC}"
echo "=================================================="
echo ""

# 创建文档目录
mkdir -p "$DOCS_DIR"

# 函数：打印带颜色的消息
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# 函数：检查系统要求
check_system_requirements() {
    print_info "检查系统要求..."
    
    # 检查 macOS
    if [[ "$OSTYPE" != "darwin"* ]]; then
        print_error "此脚本只能在 macOS 上运行"
        exit 1
    fi
    print_status "运行在 macOS 系统"
    
    # 检查 Xcode Command Line Tools
    if ! command -v codesign &> /dev/null; then
        print_warning "未找到 codesign 工具"
        print_info "正在安装 Xcode Command Line Tools..."
        xcode-select --install
        print_info "请完成 Xcode Command Line Tools 安装后重新运行此脚本"
        exit 1
    fi
    print_status "codesign 工具可用"
    
    # 检查钥匙串访问
    if ! command -v security &> /dev/null; then
        print_error "未找到 security 工具"
        exit 1
    fi
    print_status "security 工具可用"
}

# 函数：检查现有证书
check_existing_certificates() {
    print_info "检查现有的代码签名证书..."
    
    # 查找所有代码签名证书
    CERTIFICATES=$(security find-identity -v -p codesigning 2>/dev/null | grep "Developer ID Application" || true)
    
    if [ -z "$CERTIFICATES" ]; then
        print_warning "未找到 Developer ID Application 证书"
        return 1
    else
        print_status "找到以下证书："
        echo "$CERTIFICATES"
        
        # 提取第一个证书的名称
        CERT_NAME=$(echo "$CERTIFICATES" | head -n1 | sed 's/.*"\(.*\)".*/\1/')
        echo ""
        print_status "将使用证书: $CERT_NAME"
        
        # 保存证书信息到文件
        echo "DEVELOPER_ID_CERT=\"$CERT_NAME\"" > "$PROJECT_ROOT/.codesign-config"
        print_status "证书信息已保存到 .codesign-config"
        
        return 0
    fi
}

# 函数：显示证书申请指导
show_certificate_guide() {
    print_warning "需要申请 Apple Developer ID 证书"
    echo ""
    print_info "请按照以下步骤申请证书："
    echo ""
    echo "1. 📖 查看详细指南："
    echo "   open '$DOCS_DIR/申请Apple开发者证书指南.md'"
    echo ""
    echo "2. 🌐 访问 Apple Developer 网站："
    echo "   open 'https://developer.apple.com/account/'"
    echo ""
    echo "3. 📋 申请步骤概要："
    echo "   - 使用 Apple ID 登录开发者网站"
    echo "   - 注册免费开发者账户"
    echo "   - 生成证书签名请求 (CSR)"
    echo "   - 创建 Developer ID Application 证书"
    echo "   - 下载并安装证书"
    echo ""
    
    read -p "是否现在打开申请指南？(y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        if command -v open &> /dev/null; then
            open "$DOCS_DIR/申请Apple开发者证书指南.md"
        else
            print_info "请手动打开文件: $DOCS_DIR/申请Apple开发者证书指南.md"
        fi
    fi
}

# 函数：生成 CSR 文件
generate_csr() {
    print_info "是否需要生成证书签名请求 (CSR) 文件？"
    read -p "输入 y 生成 CSR，输入 n 跳过: " -n 1 -r
    echo
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        print_info "请输入以下信息来生成 CSR："
        
        read -p "邮箱地址 (Apple ID): " EMAIL
        read -p "姓名: " NAME
        
        CSR_PATH="$PROJECT_ROOT/CertificateSigningRequest.certSigningRequest"
        
        # 生成私钥和 CSR
        openssl req -new -newkey rsa:2048 -nodes -keyout "$PROJECT_ROOT/private.key" -out "$CSR_PATH" -subj "/emailAddress=$EMAIL/CN=$NAME/C=CN"
        
        if [ -f "$CSR_PATH" ]; then
            print_status "CSR 文件已生成: $CSR_PATH"
            print_info "请将此文件上传到 Apple Developer 网站"
        else
            print_error "CSR 文件生成失败"
        fi
    fi
}

# 函数：测试代码签名
test_codesigning() {
    if [ -f "$PROJECT_ROOT/.codesign-config" ]; then
        source "$PROJECT_ROOT/.codesign-config"
        
        print_info "测试代码签名功能..."
        
        # 创建测试文件
        TEST_FILE="$PROJECT_ROOT/test-sign.txt"
        echo "Test file for codesigning" > "$TEST_FILE"
        
        # 尝试签名
        if codesign --force --sign "$DEVELOPER_ID_CERT" "$TEST_FILE" 2>/dev/null; then
            print_status "代码签名测试成功"
            
            # 验证签名
            if codesign --verify --verbose "$TEST_FILE" 2>/dev/null; then
                print_status "签名验证成功"
            else
                print_warning "签名验证失败"
            fi
        else
            print_error "代码签名测试失败"
        fi
        
        # 清理测试文件
        rm -f "$TEST_FILE"
    fi
}

# 函数：创建配置文件
create_config_files() {
    print_info "创建项目配置文件..."
    
    # 创建环境变量文件
    cat > "$PROJECT_ROOT/.env.codesign" << EOF
# Apple Developer ID 代码签名配置
# 此文件包含代码签名所需的环境变量

# 证书名称 (从 .codesign-config 文件中读取)
# DEVELOPER_ID_CERT="Developer ID Application: Your Name (TEAM_ID)"

# 是否启用代码签名 (true/false)
ENABLE_CODESIGNING=true

# 是否启用强化运行时 (true/false)
ENABLE_HARDENED_RUNTIME=true

# 是否启用公证 (true/false) - 需要付费开发者账户
ENABLE_NOTARIZATION=false

# 构建目标架构
BUILD_TARGETS=x64,arm64

# DMG 签名设置
SIGN_DMG=true
EOF
    
    print_status "已创建 .env.codesign 配置文件"
}

# 主函数
main() {
    echo "开始设置 Apple Developer ID 代码签名环境..."
    echo ""
    
    # 检查系统要求
    check_system_requirements
    echo ""
    
    # 检查现有证书
    if check_existing_certificates; then
        echo ""
        test_codesigning
        echo ""
        create_config_files
        echo ""
        print_status "代码签名环境设置完成！"
        print_info "下一步：运行 'npm run build:mac' 来构建签名的应用"
    else
        echo ""
        show_certificate_guide
        echo ""
        generate_csr
        echo ""
        print_warning "请完成证书申请后重新运行此脚本"
        print_info "运行命令: ./scripts/setup-codesigning.sh"
    fi
}

# 运行主函数
main "$@"
