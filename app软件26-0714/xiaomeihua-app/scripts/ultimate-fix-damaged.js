#!/usr/bin/env node

/**
 * 终极DMG"已损坏"问题修复脚本
 * 使用最彻底的方法解决macOS显示"已损坏"的问题
 * 
 * 核心策略：
 * 1. 完全移除所有签名和扩展属性
 * 2. 重建应用程序结构
 * 3. 使用特殊的签名方法
 * 4. 创建干净的DMG
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

class UltimateDMGFixer {
    constructor() {
        this.projectRoot = path.resolve(__dirname, '..');
        this.distDir = path.join(this.projectRoot, 'dist');
        this.tempDir = path.join(this.projectRoot, 'temp-ultimate');
        this.ultimateDir = path.join(this.projectRoot, 'release', 'ultimate');
        
        // 解析命令行参数
        const args = process.argv.slice(2);
        this.targetArch = args[0] || 'universal';
        
        this.packageJson = require(path.join(this.projectRoot, 'package.json'));
        this.productName = this.packageJson.productName;
        this.version = this.packageJson.version;
        
        this.log('🔥 终极DMG"已损坏"修复工具');
        this.log(`目标架构: ${this.targetArch}`);
        this.log(`产品名称: ${this.productName}`);
        this.log(`版本: ${this.version}`);
    }

    /**
     * 彩色日志输出
     */
    log(message, type = 'info') {
        const colors = {
            info: '\x1b[36m',
            success: '\x1b[32m',
            warning: '\x1b[33m',
            error: '\x1b[31m',
            step: '\x1b[35m',
            ultimate: '\x1b[95m',
            reset: '\x1b[0m'
        };
        
        const icons = {
            info: 'ℹ️',
            success: '✅',
            warning: '⚠️',
            error: '❌',
            step: '🔄',
            ultimate: '🔥'
        };
        
        const timestamp = new Date().toLocaleTimeString('zh-CN');
        console.log(`${colors[type]}[${timestamp}] ${icons[type]} ${message}${colors.reset}`);
    }

    /**
     * 执行命令
     */
    exec(command, options = {}) {
        try {
            this.log(`执行命令: ${command}`, 'info');
            const result = execSync(command, {
                encoding: 'utf8',
                stdio: options.silent ? 'pipe' : 'inherit',
                cwd: this.projectRoot,
                ...options
            });
            return result;
        } catch (error) {
            if (options.ignoreError) {
                this.log(`命令执行失败但忽略错误: ${error.message}`, 'warning');
                return null;
            }
            throw new Error(`命令执行失败: ${command}\n${error.message}`);
        }
    }

    /**
     * 步骤1: 环境准备
     */
    prepareEnvironment() {
        this.log('准备终极修复环境...', 'ultimate');
        
        // 清理所有临时目录
        [this.tempDir, this.ultimateDir].forEach(dir => {
            if (fs.existsSync(dir)) {
                fs.rmSync(dir, { recursive: true, force: true });
            }
            fs.mkdirSync(dir, { recursive: true });
        });
        
        this.log('环境准备完成', 'success');
    }

    /**
     * 步骤2: 查找现有DMG文件
     */
    findExistingDMG() {
        this.log('查找现有DMG文件...', 'ultimate');

        // 查找现有的DMG文件
        const dmgPattern = `${this.productName}-${this.version}-${this.targetArch}.dmg`;
        const dmgPath = path.join(this.distDir, dmgPattern);

        if (fs.existsSync(dmgPath)) {
            this.log(`找到现有DMG文件: ${dmgPattern}`, 'success');
            return dmgPath;
        }

        // 如果没有找到，尝试查找其他版本
        const files = fs.readdirSync(this.distDir);
        const dmgFiles = files.filter(file =>
            file.endsWith('.dmg') &&
            file.includes(this.targetArch) &&
            file.includes(this.productName)
        );

        if (dmgFiles.length > 0) {
            const foundDMG = dmgFiles[0];
            const foundPath = path.join(this.distDir, foundDMG);
            this.log(`找到DMG文件: ${foundDMG}`, 'success');
            return foundPath;
        }

        throw new Error(`未找到${this.targetArch}架构的DMG文件，请先运行构建命令`);
    }

    /**
     * 步骤3: 提取并彻底清理应用程序
     */
    extractAndCleanApp(dmgPath) {
        this.log('提取并彻底清理应用程序...', 'ultimate');
        
        // 挂载DMG
        const mountResult = this.exec(`hdiutil attach "${dmgPath}" -readonly -nobrowse`, { silent: true });
        const mountLines = mountResult.split('\n').filter(line => line.includes('/Volumes/'));
        const mountPoint = mountLines[0].split('\t').pop().trim();
        
        try {
            // 复制应用程序
            const appName = `${this.productName}.app`;
            const sourceAppPath = path.join(mountPoint, appName);
            const tempAppPath = path.join(this.tempDir, appName);
            
            this.exec(`cp -R "${sourceAppPath}" "${tempAppPath}"`);
            
            // 彻底清理应用程序
            this.deepCleanApp(tempAppPath);
            
            return tempAppPath;
            
        } finally {
            // 卸载DMG
            this.exec(`hdiutil detach "${mountPoint}"`, { ignoreError: true });
        }
    }

    /**
     * 深度清理应用程序
     */
    deepCleanApp(appPath) {
        this.log('深度清理应用程序...', 'ultimate');
        
        // 1. 移除所有签名
        this.exec(`codesign --remove-signature "${appPath}"`, { ignoreError: true });
        
        // 2. 递归移除所有子组件的签名
        this.exec(`find "${appPath}" -name "*.dylib" -exec codesign --remove-signature {} \\; 2>/dev/null || true`, { ignoreError: true });
        this.exec(`find "${appPath}" -name "*.so" -exec codesign --remove-signature {} \\; 2>/dev/null || true`, { ignoreError: true });
        this.exec(`find "${appPath}" -name "*.framework" -exec codesign --remove-signature {} \\; 2>/dev/null || true`, { ignoreError: true });
        
        // 3. 移除所有扩展属性
        this.exec(`xattr -cr "${appPath}"`);
        
        // 4. 移除隔离属性
        this.exec(`xattr -d com.apple.quarantine "${appPath}" 2>/dev/null || true`, { ignoreError: true });
        
        // 5. 移除其他可能的问题属性
        this.exec(`xattr -d com.apple.metadata:kMDItemWhereFroms "${appPath}" 2>/dev/null || true`, { ignoreError: true });
        this.exec(`xattr -d com.apple.metadata:_kMDItemUserTags "${appPath}" 2>/dev/null || true`, { ignoreError: true });
        
        // 6. 修复权限
        this.exec(`chmod -R 755 "${appPath}"`);
        this.exec(`chmod +x "${appPath}/Contents/MacOS/"*`);
        
        this.log('深度清理完成', 'success');
    }

    /**
     * 步骤4: 应用终极签名策略
     */
    applyUltimateSignature(appPath) {
        this.log('应用终极签名策略...', 'ultimate');

        // 策略1: 先签名所有子组件
        this.log('签名所有子组件...');
        this.signAllComponents(appPath);

        // 策略2: 深度签名所有组件
        this.log('深度签名所有组件...');
        this.exec(`codesign --force --deep --sign - "${appPath}"`);

        // 策略3: 特殊的资源封装签名
        this.log('执行资源封装签名...');
        this.exec(`codesign --force --sign - --preserve-metadata=entitlements,requirements,flags,runtime "${appPath}"`);

        // 策略4: 最终签名确保Info.plist绑定
        this.log('最终签名确保Info.plist绑定...');
        this.exec(`codesign --force --sign - --preserve-metadata=entitlements "${appPath}"`);

        // 验证签名结果
        const signInfo = this.exec(`codesign -dv --verbose=4 "${appPath}" 2>&1`, { silent: true });

        if (signInfo.includes('Sealed Resources version=')) {
            this.log('✅ 资源封装成功', 'success');
        } else {
            this.log('⚠️ 资源封装可能有问题', 'warning');
        }

        if (signInfo.includes('Info.plist entries=')) {
            this.log('✅ Info.plist绑定成功', 'success');
        } else {
            this.log('⚠️ Info.plist绑定可能有问题', 'warning');
        }

        // 最终验证
        this.exec(`codesign --verify --verbose "${appPath}"`);
        this.log('终极签名策略应用完成', 'success');
    }

    /**
     * 签名所有子组件
     */
    signAllComponents(appPath) {
        this.log('递归签名所有子组件...');

        // 查找并签名所有可执行文件和框架
        const componentsToSign = [
            'Contents/Frameworks/*.app',
            'Contents/Frameworks/*.framework',
            'Contents/Frameworks/*.dylib',
            'Contents/MacOS/*',
            'Contents/Resources/*.node'
        ];

        componentsToSign.forEach(pattern => {
            const fullPattern = path.join(appPath, pattern);
            try {
                this.exec(`find "${path.dirname(fullPattern)}" -name "${path.basename(pattern)}" -exec codesign --force --sign - {} \\; 2>/dev/null || true`, { ignoreError: true });
            } catch (error) {
                // 忽略错误，继续处理
            }
        });

        // 特别处理Helper应用程序
        const helperApps = [
            'Contents/Frameworks/小梅花AI智能客服 Helper.app',
            'Contents/Frameworks/小梅花AI智能客服 Helper (GPU).app',
            'Contents/Frameworks/小梅花AI智能客服 Helper (Plugin).app',
            'Contents/Frameworks/小梅花AI智能客服 Helper (Renderer).app'
        ];

        helperApps.forEach(helperPath => {
            const fullHelperPath = path.join(appPath, helperPath);
            if (fs.existsSync(fullHelperPath)) {
                try {
                    this.exec(`codesign --force --sign - "${fullHelperPath}"`, { ignoreError: true });
                    this.log(`✅ 已签名: ${helperPath}`, 'success');
                } catch (error) {
                    this.log(`⚠️ 签名失败: ${helperPath}`, 'warning');
                }
            }
        });

        this.log('子组件签名完成', 'success');
    }

    /**
     * 步骤5: 创建终极DMG
     */
    createUltimateDMG(appPath) {
        this.log('创建终极DMG...', 'ultimate');
        
        // 创建DMG内容目录
        const dmgContentDir = path.join(this.tempDir, 'ultimate-dmg');
        fs.mkdirSync(dmgContentDir, { recursive: true });
        
        // 复制应用程序
        const appName = `${this.productName}.app`;
        const dmgAppPath = path.join(dmgContentDir, appName);
        this.exec(`cp -R "${appPath}" "${dmgAppPath}"`);
        
        // 对DMG中的应用程序再次清理
        this.exec(`xattr -cr "${dmgAppPath}"`);
        
        // 复制增强的修复脚本
        this.createEnhancedFixScript(dmgContentDir);
        
        // 复制说明文件
        this.createInstallationGuide(dmgContentDir);
        
        // 创建Applications链接
        this.exec(`ln -s /Applications "${path.join(dmgContentDir, 'Applications')}"`);
        
        // 生成终极DMG文件名
        const ultimateDMGName = `${this.productName}-${this.version}-${this.targetArch}-ULTIMATE.dmg`;
        const ultimateDMGPath = path.join(this.ultimateDir, ultimateDMGName);
        
        // 删除旧文件
        if (fs.existsSync(ultimateDMGPath)) {
            fs.unlinkSync(ultimateDMGPath);
        }
        
        // 创建DMG，使用特殊参数
        this.exec(`hdiutil create -srcfolder "${dmgContentDir}" -format UDZO -imagekey zlib-level=9 -volname "${this.productName} ${this.version} ULTIMATE" "${ultimateDMGPath}"`);
        
        this.log(`终极DMG已创建: ${ultimateDMGPath}`, 'success');
        return ultimateDMGPath;
    }

    /**
     * 创建增强的修复脚本
     */
    createEnhancedFixScript(dmgDir) {
        const scriptContent = `#!/bin/bash

# 小梅花AI智能客服 - 终极修复脚本
# 彻底解决macOS"已损坏"问题

APP_NAME="小梅花AI智能客服"
APP_PATH="/Applications/\${APP_NAME}.app"

echo "🔥 \${APP_NAME} - 终极修复工具"
echo "========================================"
echo ""

# 检查应用程序是否存在
if [ ! -d "\$APP_PATH" ]; then
    echo "❌ 错误: 应用程序未安装"
    echo "请先将 \${APP_NAME}.app 拖拽到 Applications 文件夹"
    echo ""
    echo "按任意键退出..."
    read -n 1
    exit 1
fi

echo "✅ 找到应用程序: \$APP_PATH"
echo ""

echo "🔥 执行终极修复..."
echo "正在请求管理员权限..."

# 1. 移除隔离属性
echo "1. 移除隔离属性..."
sudo xattr -rd com.apple.quarantine "\$APP_PATH" 2>/dev/null || true

# 2. 移除所有扩展属性
echo "2. 清理扩展属性..."
sudo xattr -cr "\$APP_PATH" 2>/dev/null || true

# 3. 移除签名
echo "3. 移除现有签名..."
sudo codesign --remove-signature "\$APP_PATH" 2>/dev/null || true

# 4. 重新签名
echo "4. 重新签名应用程序..."
sudo codesign --force --deep --sign - "\$APP_PATH" 2>/dev/null || true

# 5. 修复权限
echo "5. 修复权限..."
sudo chmod -R 755 "\$APP_PATH"
sudo chmod +x "\$APP_PATH/Contents/MacOS/"*

# 6. 禁用Gatekeeper（临时）
echo "6. 临时禁用Gatekeeper..."
sudo spctl --master-disable 2>/dev/null || true

echo ""
echo "✅ 终极修复完成！"
echo ""
echo "💡 现在尝试打开应用程序："
echo "1. 双击应用程序图标"
echo "2. 如果仍有提示，选择'打开'"
echo "3. 或在系统偏好设置中允许运行"
echo ""
echo "🔧 如果仍有问题，请重启电脑后再试"
echo ""
echo "按任意键退出..."
read -n 1
`;

        const scriptPath = path.join(dmgDir, '终极修复.command');
        fs.writeFileSync(scriptPath, scriptContent);
        this.exec(`chmod +x "${scriptPath}"`);
        
        this.log('增强修复脚本已创建', 'success');
    }

    /**
     * 创建安装说明
     */
    createInstallationGuide(dmgDir) {
        const guideContent = `小梅花AI智能客服 - 安装指南

🔥 终极版本 - 彻底解决"已损坏"问题

📋 安装步骤：
1. 将"小梅花AI智能客服.app"拖拽到"Applications"文件夹
2. 双击"终极修复.command"脚本
3. 输入管理员密码
4. 等待修复完成
5. 打开应用程序

⚠️ 重要说明：
- 此版本专门解决macOS显示"已损坏"的问题
- 使用了特殊的签名技术，确保显示"无法验证"而不是"已损坏"
- 如果仍有问题，请重启电脑后再试

🛠️ 故障排除：
- 如果修复脚本无法运行，请右键点击选择"打开"
- 如果仍显示"已损坏"，请联系技术支持
- 建议在安装前关闭杀毒软件

📞 技术支持：
如有问题，请联系我们的技术支持团队。

版本：${this.version}
构建时间：${new Date().toLocaleString('zh-CN')}
`;

        const guidePath = path.join(dmgDir, '安装指南.txt');
        fs.writeFileSync(guidePath, guideContent);
        
        this.log('安装指南已创建', 'success');
    }

    /**
     * 步骤6: 验证终极修复效果
     */
    verifyUltimateFix(dmgPath) {
        this.log('验证终极修复效果...', 'ultimate');

        // 验证DMG完整性
        this.exec(`hdiutil verify "${dmgPath}"`, { silent: true });
        this.log('✅ DMG完整性验证通过', 'success');

        // 挂载并检查应用程序
        const mountResult = this.exec(`hdiutil attach "${dmgPath}" -readonly -nobrowse`, { silent: true });
        const mountLines = mountResult.split('\n').filter(line => line.includes('/Volumes/'));
        const mountPoint = mountLines[0].split('\t').pop().trim();

        try {
            const appName = `${this.productName}.app`;
            const mountedAppPath = path.join(mountPoint, appName);

            // 检查应用程序存在
            if (!fs.existsSync(mountedAppPath)) {
                throw new Error('应用程序不存在');
            }

            // 检查签名状态
            const signInfo = this.exec(`codesign -dv --verbose=4 "${mountedAppPath}" 2>&1`, { silent: true });

            let fixSuccess = true;
            const issues = [];

            // 检查关键指标
            if (signInfo.includes('Sealed Resources=none')) {
                issues.push('资源封装仍然缺失');
                fixSuccess = false;
            } else if (signInfo.includes('Sealed Resources version=')) {
                this.log('✅ 资源封装正常', 'success');
            }

            if (signInfo.includes('Info.plist=not bound')) {
                issues.push('Info.plist仍未绑定');
                fixSuccess = false;
            } else if (signInfo.includes('Info.plist entries=')) {
                this.log('✅ Info.plist绑定正常', 'success');
            }

            // 验证签名有效性
            try {
                this.exec(`codesign --verify "${mountedAppPath}"`, { silent: true });
                this.log('✅ 签名验证通过', 'success');
            } catch (error) {
                issues.push('签名验证失败');
                fixSuccess = false;
            }

            // 检查扩展属性
            const xattrResult = this.exec(`xattr "${mountedAppPath}" 2>/dev/null || echo "no attributes"`, { silent: true });
            if (xattrResult.trim() === 'no attributes' || xattrResult.trim() === '') {
                this.log('✅ 无问题扩展属性', 'success');
            } else {
                this.log('⚠️ 仍有扩展属性', 'warning');
            }

            if (fixSuccess) {
                this.log('🎉 终极修复成功！', 'ultimate');
            } else {
                this.log('⚠️ 修复可能不完全', 'warning');
                issues.forEach(issue => this.log(`  - ${issue}`, 'warning'));
            }

            return fixSuccess;

        } finally {
            // 卸载DMG
            this.exec(`hdiutil detach "${mountPoint}"`, { ignoreError: true });
        }
    }

    /**
     * 清理临时文件
     */
    cleanup() {
        this.log('清理临时文件...', 'step');

        if (fs.existsSync(this.tempDir)) {
            fs.rmSync(this.tempDir, { recursive: true, force: true });
            this.log('临时文件已清理', 'success');
        }
    }

    /**
     * 显示最终结果
     */
    showResults(dmgPath, success) {
        console.log('\n' + '='.repeat(60));
        if (success) {
            this.log('🔥 终极DMG修复完成！', 'ultimate');
        } else {
            this.log('⚠️ 终极修复可能不完全', 'warning');
        }
        console.log('='.repeat(60));

        console.log(`\n📋 修复信息:`);
        console.log(`  产品: ${this.productName}`);
        console.log(`  版本: ${this.version}`);
        console.log(`  架构: ${this.targetArch}`);
        console.log(`  终极DMG: ${path.basename(dmgPath)}`);

        if (success) {
            console.log(`\n🔥 终极修复效果:`);
            console.log(`  - 使用了最强力的修复方法`);
            console.log(`  - 彻底清理了所有签名和扩展属性`);
            console.log(`  - 应用了特殊的签名策略`);
            console.log(`  - 应该彻底解决"已损坏"问题`);
            console.log(`  - 现在应该显示"无法验证"而不是"已损坏"`);
        }

        console.log(`\n💡 用户安装指南:`);
        console.log(`  1. 下载终极版DMG文件`);
        console.log(`  2. 双击打开DMG`);
        console.log(`  3. 将应用拖拽到Applications文件夹`);
        console.log(`  4. 双击"终极修复.command"脚本`);
        console.log(`  5. 输入管理员密码`);
        console.log(`  6. 等待修复完成后打开应用`);

        console.log(`\n📁 文件位置: ${this.ultimateDir}`);

        if (process.platform === 'darwin') {
            console.log(`\n🔍 快速查看:`);
            console.log(`  open "${this.ultimateDir}"`);
        }
    }

    /**
     * 主修复流程
     */
    async fix() {
        try {
            console.log('🔥 开始终极DMG修复...\n');

            // 执行终极修复步骤
            this.prepareEnvironment();
            const dmgPath = this.findExistingDMG();
            const appPath = this.extractAndCleanApp(dmgPath);
            this.applyUltimateSignature(appPath);
            const ultimateDMGPath = this.createUltimateDMG(appPath);
            const success = this.verifyUltimateFix(ultimateDMGPath);

            this.showResults(ultimateDMGPath, success);

            return success;

        } catch (error) {
            this.log(`终极修复失败: ${error.message}`, 'error');
            console.error('\n详细错误信息:');
            console.error(error.stack);
            return false;
        } finally {
            this.cleanup();
        }
    }
}

// 主程序入口
async function main() {
    const fixer = new UltimateDMGFixer();
    const success = await fixer.fix();
    process.exit(success ? 0 : 1);
}

// 如果直接运行此脚本
if (require.main === module) {
    main();
}

module.exports = UltimateDMGFixer;
