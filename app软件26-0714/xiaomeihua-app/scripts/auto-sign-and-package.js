#!/usr/bin/env node

/**
 * 自动签名和打包脚本
 * 完整的应用签名、打包、验证流程
 */

const { execSync, spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

class AutoSignPackager {
    constructor() {
        this.projectRoot = path.resolve(__dirname, '..');
        this.distDir = path.join(this.projectRoot, 'dist');
        this.releaseDir = path.join(this.projectRoot, 'release');
        this.configFile = path.join(this.projectRoot, '.codesign-config');
        
        // 解析命令行参数
        const args = process.argv.slice(2);
        const flags = args.filter(arg => arg.startsWith('--'));
        const archArg = args.find(arg => !arg.startsWith('--'));

        // 构建选项
        this.options = {
            arch: archArg || 'universal', // x64, arm64, universal
            skipSigning: flags.includes('--no-sign'),
            skipVerification: flags.includes('--no-verify'),
            skipPackaging: flags.includes('--no-package'),
            verbose: flags.includes('--verbose')
        };
        
        this.log('🍎 自动签名和打包工具');
        this.log(`架构: ${this.options.arch}`);
        this.log(`选项: ${JSON.stringify(this.options, null, 2)}`);
    }

    /**
     * 日志输出
     */
    log(message, level = 'info') {
        const timestamp = new Date().toLocaleTimeString();
        const prefix = {
            info: '📝',
            success: '✅',
            warning: '⚠️',
            error: '❌'
        }[level] || 'ℹ️';
        
        console.log(`${prefix} [${timestamp}] ${message}`);
    }

    /**
     * 检查前置条件
     */
    checkPrerequisites() {
        this.log('检查前置条件...');
        
        // 检查 macOS
        if (process.platform !== 'darwin') {
            this.log('此脚本只能在 macOS 上运行', 'error');
            return false;
        }
        
        // 检查 Node.js 版本
        const nodeVersion = process.version;
        this.log(`Node.js 版本: ${nodeVersion}`);
        
        // 检查必要工具
        const tools = ['codesign', 'hdiutil', 'security'];
        for (const tool of tools) {
            try {
                execSync(`which ${tool}`, { stdio: 'pipe' });
                this.log(`${tool} 工具可用`, 'success');
            } catch (error) {
                this.log(`${tool} 工具不可用`, 'error');
                return false;
            }
        }
        
        // 检查项目依赖
        if (!fs.existsSync(path.join(this.projectRoot, 'node_modules'))) {
            this.log('正在安装项目依赖...', 'warning');
            execSync('npm install', { stdio: 'inherit', cwd: this.projectRoot });
        }
        
        return true;
    }

    /**
     * 检查代码签名配置
     */
    checkSigningConfig() {
        if (this.options.skipSigning) {
            this.log('跳过代码签名检查', 'warning');
            return true;
        }
        
        this.log('检查代码签名配置...');
        
        if (!fs.existsSync(this.configFile)) {
            this.log('未找到代码签名配置文件', 'warning');
            this.log('请运行: npm run setup:codesign', 'warning');
            return false;
        }
        
        const configContent = fs.readFileSync(this.configFile, 'utf8');
        const match = configContent.match(/DEVELOPER_ID_CERT="(.+)"/);
        
        if (!match) {
            this.log('代码签名配置无效', 'error');
            return false;
        }
        
        const certName = match[1];
        this.log(`使用证书: ${certName}`);
        
        // 验证证书是否存在
        try {
            const result = execSync(`security find-identity -v -p codesigning | grep "${certName}"`, { encoding: 'utf8' });
            if (result.trim()) {
                this.log('证书验证通过', 'success');
                return true;
            }
        } catch (error) {
            this.log('证书验证失败', 'error');
            return false;
        }
        
        return false;
    }

    /**
     * 清理环境
     */
    cleanEnvironment() {
        this.log('清理构建环境...');
        
        // 清理 dist 目录
        if (fs.existsSync(this.distDir)) {
            execSync('rm -rf dist/*', { cwd: this.projectRoot });
            this.log('已清理 dist 目录', 'success');
        }
        
        // 创建 release 目录
        if (!fs.existsSync(this.releaseDir)) {
            fs.mkdirSync(this.releaseDir, { recursive: true });
            this.log('已创建 release 目录', 'success');
        }
    }

    /**
     * 构建应用
     */
    async buildApplication() {
        this.log(`开始构建 ${this.options.arch} 架构的应用...`);
        
        try {
            if (this.options.skipSigning) {
                // 无签名构建
                this.log('构建无签名版本...', 'warning');
                execSync(`npm run build:dmg:${this.options.arch}`, { 
                    stdio: 'inherit', 
                    cwd: this.projectRoot 
                });
            } else {
                // 签名构建
                this.log('构建签名版本...');
                execSync(`npm run build:signed:${this.options.arch}`, { 
                    stdio: 'inherit', 
                    cwd: this.projectRoot 
                });
            }
            
            this.log('应用构建完成', 'success');
            return true;
        } catch (error) {
            this.log(`构建失败: ${error.message}`, 'error');
            return false;
        }
    }

    /**
     * 验证构建结果
     */
    async verifyBuild() {
        if (this.options.skipVerification) {
            this.log('跳过构建验证', 'warning');
            return true;
        }
        
        this.log('验证构建结果...');
        
        try {
            execSync('npm run verify:codesign', { 
                stdio: this.options.verbose ? 'inherit' : 'pipe', 
                cwd: this.projectRoot 
            });
            this.log('构建验证通过', 'success');
            return true;
        } catch (error) {
            this.log('构建验证失败', 'warning');
            if (this.options.verbose) {
                this.log(error.message, 'error');
            }
            return false;
        }
    }

    /**
     * 打包发布文件
     */
    async packageRelease() {
        if (this.options.skipPackaging) {
            this.log('跳过发布打包', 'warning');
            return true;
        }
        
        this.log('打包发布文件...');
        
        const distFiles = fs.readdirSync(this.distDir);
        const dmgFiles = distFiles.filter(file => file.endsWith('.dmg'));
        
        if (dmgFiles.length === 0) {
            this.log('未找到 DMG 文件', 'error');
            return false;
        }
        
        // 复制 DMG 文件到 release 目录
        for (const dmgFile of dmgFiles) {
            const srcPath = path.join(this.distDir, dmgFile);
            const destPath = path.join(this.releaseDir, dmgFile);
            
            fs.copyFileSync(srcPath, destPath);
            this.log(`已复制: ${dmgFile}`, 'success');
            
            // 生成校验和
            const checksum = execSync(`shasum -a 256 "${destPath}"`, { encoding: 'utf8' });
            const checksumFile = destPath + '.sha256';
            fs.writeFileSync(checksumFile, checksum);
            this.log(`已生成校验和: ${path.basename(checksumFile)}`, 'success');
        }
        
        return true;
    }

    /**
     * 生成发布说明
     */
    generateReleaseNotes() {
        this.log('生成发布说明...');
        
        const packageJson = require(path.join(this.projectRoot, 'package.json'));
        const releaseFiles = fs.readdirSync(this.releaseDir);
        const dmgFiles = releaseFiles.filter(file => file.endsWith('.dmg'));
        
        const releaseNotes = `# ${packageJson.productName} v${packageJson.version}

## 发布信息
- 构建时间: ${new Date().toLocaleString('zh-CN')}
- 架构: ${this.options.arch}
- 代码签名: ${this.options.skipSigning ? '否' : '是'}

## 安装文件
${dmgFiles.map(file => {
    const filePath = path.join(this.releaseDir, file);
    const stats = fs.statSync(filePath);
    const size = (stats.size / 1024 / 1024).toFixed(1);
    return `- ${file} (${size}MB)`;
}).join('\n')}

## 安装说明

### macOS 用户
1. 下载对应架构的 DMG 文件
2. 双击 DMG 文件打开
3. 将应用拖动到 Applications 文件夹

${this.options.skipSigning ? `
### 重要提示
此版本未进行代码签名，首次运行时：
1. 右键点击应用图标
2. 选择"打开"
3. 在弹出对话框中点击"打开"

或者在"系统偏好设置" > "安全性与隐私" > "通用"中点击"仍要打开"
` : `
### 代码签名
此版本已使用 Apple Developer ID 进行代码签名，可以正常安装运行。
`}

## 技术支持
如遇安装问题，请联系技术支持。

---
构建配置: ${JSON.stringify(this.options, null, 2)}
`;

        const notesPath = path.join(this.releaseDir, 'RELEASE_NOTES.md');
        fs.writeFileSync(notesPath, releaseNotes);
        this.log(`发布说明已保存: ${notesPath}`, 'success');
    }

    /**
     * 显示完成摘要
     */
    displaySummary() {
        this.log('构建完成摘要', 'success');
        console.log('='.repeat(60));
        
        const releaseFiles = fs.readdirSync(this.releaseDir);
        console.log(`📁 发布目录: ${this.releaseDir}`);
        console.log(`📦 生成文件: ${releaseFiles.length} 个`);
        
        releaseFiles.forEach(file => {
            const filePath = path.join(this.releaseDir, file);
            const stats = fs.statSync(filePath);
            const size = (stats.size / 1024 / 1024).toFixed(1);
            console.log(`   • ${file} (${size}MB)`);
        });
        
        console.log('\n🚀 下一步:');
        console.log('   • 测试安装包');
        console.log('   • 分发给用户');
        console.log('   • 查看发布说明: cat release/RELEASE_NOTES.md');
    }

    /**
     * 主流程
     */
    async run() {
        try {
            console.log('\n🚀 开始自动签名和打包流程...\n');
            
            // 检查前置条件
            if (!this.checkPrerequisites()) {
                process.exit(1);
            }
            
            // 检查签名配置
            if (!this.checkSigningConfig()) {
                if (!this.options.skipSigning) {
                    this.log('代码签名配置检查失败，使用 --no-sign 跳过签名', 'warning');
                    process.exit(1);
                }
            }
            
            // 清理环境
            this.cleanEnvironment();
            
            // 构建应用
            if (!await this.buildApplication()) {
                process.exit(1);
            }
            
            // 验证构建
            await this.verifyBuild();
            
            // 打包发布
            if (!await this.packageRelease()) {
                process.exit(1);
            }
            
            // 生成发布说明
            this.generateReleaseNotes();
            
            // 显示摘要
            this.displaySummary();
            
            this.log('🎉 自动签名和打包完成！', 'success');
            
        } catch (error) {
            this.log(`流程失败: ${error.message}`, 'error');
            process.exit(1);
        }
    }
}

// 显示帮助信息
if (process.argv.includes('--help') || process.argv.includes('-h')) {
    console.log(`
🍎 自动签名和打包工具

用法:
  node scripts/auto-sign-and-package.js [架构] [选项]

架构:
  x64        构建 Intel 64位版本
  arm64      构建 Apple Silicon 版本
  universal  构建通用版本 (默认)

选项:
  --no-sign     跳过代码签名
  --no-verify   跳过构建验证
  --no-package  跳过发布打包
  --verbose     显示详细输出
  --help, -h    显示此帮助信息

示例:
  npm run auto-sign-package
  npm run auto-sign-package x64
  npm run auto-sign-package -- --no-sign
  npm run auto-sign-package universal --verbose
`);
    process.exit(0);
}

// 运行主流程
if (require.main === module) {
    const packager = new AutoSignPackager();
    packager.run();
}

module.exports = AutoSignPackager;
