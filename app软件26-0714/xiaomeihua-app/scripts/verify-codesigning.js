#!/usr/bin/env node

/**
 * 代码签名验证脚本
 * 验证应用程序的代码签名状态
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

class CodeSignVerifier {
    constructor() {
        this.projectRoot = path.resolve(__dirname, '..');
        this.distDir = path.join(this.projectRoot, 'dist');
    }

    /**
     * 检查系统环境
     */
    checkEnvironment() {
        console.log('🔍 检查系统环境...');
        
        // 检查 macOS
        if (process.platform !== 'darwin') {
            console.log('❌ 此脚本只能在 macOS 上运行');
            return false;
        }
        
        // 检查 codesign 工具
        try {
            execSync('which codesign', { stdio: 'pipe' });
            console.log('✅ codesign 工具可用');
        } catch (error) {
            console.log('❌ codesign 工具不可用');
            return false;
        }
        
        return true;
    }

    /**
     * 查找可验证的文件
     */
    findVerifiableFiles() {
        console.log('📁 查找可验证的文件...');
        
        if (!fs.existsSync(this.distDir)) {
            console.log('❌ dist 目录不存在，请先构建应用');
            return [];
        }
        
        const files = fs.readdirSync(this.distDir);
        const verifiableFiles = [];
        
        // 查找 .app 文件
        const appFiles = files.filter(file => file.endsWith('.app'));
        for (const appFile of appFiles) {
            verifiableFiles.push({
                type: 'app',
                name: appFile,
                path: path.join(this.distDir, appFile)
            });
        }
        
        // 查找 .dmg 文件
        const dmgFiles = files.filter(file => file.endsWith('.dmg'));
        for (const dmgFile of dmgFiles) {
            verifiableFiles.push({
                type: 'dmg',
                name: dmgFile,
                path: path.join(this.distDir, dmgFile)
            });
        }
        
        console.log(`✅ 找到 ${verifiableFiles.length} 个可验证文件`);
        return verifiableFiles;
    }

    /**
     * 验证单个文件的签名
     */
    verifyFileSignature(file) {
        console.log(`\n🔍 验证 ${file.name}...`);
        
        const results = {
            file: file.name,
            type: file.type,
            signed: false,
            valid: false,
            certificate: null,
            teamId: null,
            errors: []
        };
        
        try {
            // 检查是否已签名
            const signInfo = execSync(`codesign -dv "${file.path}" 2>&1`, { encoding: 'utf8' });
            
            if (signInfo.includes('not signed')) {
                results.errors.push('文件未签名');
                console.log('❌ 文件未签名');
                return results;
            }
            
            results.signed = true;
            console.log('✅ 文件已签名');
            
            // 提取证书信息
            const certMatch = signInfo.match(/Authority=(.+)/);
            if (certMatch) {
                results.certificate = certMatch[1];
                console.log(`📜 证书: ${results.certificate}`);
            }
            
            // 提取 Team ID
            const teamMatch = signInfo.match(/TeamIdentifier=(.+)/);
            if (teamMatch) {
                results.teamId = teamMatch[1];
                console.log(`🏷️  Team ID: ${results.teamId}`);
            }
            
            // 验证签名有效性
            try {
                if (file.type === 'app') {
                    execSync(`codesign --verify --deep --strict "${file.path}"`, { stdio: 'pipe' });
                } else {
                    execSync(`codesign --verify "${file.path}"`, { stdio: 'pipe' });
                }
                
                results.valid = true;
                console.log('✅ 签名验证通过');
            } catch (verifyError) {
                results.errors.push('签名验证失败');
                console.log('❌ 签名验证失败');
            }
            
        } catch (error) {
            results.errors.push(error.message);
            console.log(`❌ 验证过程出错: ${error.message}`);
        }
        
        return results;
    }

    /**
     * 检查 Gatekeeper 状态
     */
    checkGatekeeperStatus(file) {
        console.log(`\n🛡️  检查 ${file.name} 的 Gatekeeper 状态...`);
        
        try {
            // 检查隔离属性
            const xattrResult = execSync(`xattr "${file.path}" 2>/dev/null || true`, { encoding: 'utf8' });
            
            if (xattrResult.includes('com.apple.quarantine')) {
                console.log('⚠️  文件被隔离 (quarantine)');
                return {
                    quarantined: true,
                    gatekeeperFriendly: false
                };
            } else {
                console.log('✅ 文件未被隔离');
            }
            
            // 对于已签名的文件，检查 Gatekeeper 接受度
            if (file.type === 'app') {
                try {
                    execSync(`spctl --assess --type execute "${file.path}"`, { stdio: 'pipe' });
                    console.log('✅ Gatekeeper 接受此应用');
                    return {
                        quarantined: false,
                        gatekeeperFriendly: true
                    };
                } catch (error) {
                    console.log('⚠️  Gatekeeper 可能会阻止此应用');
                    return {
                        quarantined: false,
                        gatekeeperFriendly: false
                    };
                }
            }
            
        } catch (error) {
            console.log(`❌ Gatekeeper 检查失败: ${error.message}`);
        }
        
        return {
            quarantined: false,
            gatekeeperFriendly: false
        };
    }

    /**
     * 生成验证报告
     */
    generateReport(results) {
        console.log('\n📋 生成验证报告...');
        
        const report = {
            verificationTime: new Date().toISOString(),
            summary: {
                totalFiles: results.length,
                signedFiles: results.filter(r => r.signed).length,
                validFiles: results.filter(r => r.valid).length,
                unsignedFiles: results.filter(r => !r.signed).length
            },
            files: results,
            recommendations: []
        };
        
        // 生成建议
        if (report.summary.unsignedFiles > 0) {
            report.recommendations.push('建议对未签名的文件进行代码签名');
        }
        
        if (report.summary.signedFiles > 0 && report.summary.validFiles < report.summary.signedFiles) {
            report.recommendations.push('部分签名文件验证失败，请检查证书状态');
        }
        
        if (report.summary.validFiles === report.summary.totalFiles && report.summary.totalFiles > 0) {
            report.recommendations.push('所有文件签名正常，可以安全分发');
        }
        
        // 保存报告
        const reportPath = path.join(this.projectRoot, 'codesign-verification-report.json');
        fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
        console.log(`✅ 验证报告已保存: ${reportPath}`);
        
        return report;
    }

    /**
     * 显示摘要
     */
    displaySummary(report) {
        console.log('\n📊 验证摘要');
        console.log('='.repeat(50));
        console.log(`总文件数: ${report.summary.totalFiles}`);
        console.log(`已签名: ${report.summary.signedFiles}`);
        console.log(`签名有效: ${report.summary.validFiles}`);
        console.log(`未签名: ${report.summary.unsignedFiles}`);
        
        if (report.recommendations.length > 0) {
            console.log('\n💡 建议:');
            for (const recommendation of report.recommendations) {
                console.log(`   • ${recommendation}`);
            }
        }
        
        console.log('\n🔗 相关命令:');
        console.log('   • 设置代码签名: npm run setup:codesign');
        console.log('   • 构建签名版本: npm run build:signed');
        console.log('   • 查看详细信息: cat codesign-verification-report.json');
    }

    /**
     * 主验证流程
     */
    async verify() {
        try {
            console.log('🔍 代码签名验证工具\n');
            
            // 检查环境
            if (!this.checkEnvironment()) {
                process.exit(1);
            }
            
            // 查找文件
            const files = this.findVerifiableFiles();
            if (files.length === 0) {
                console.log('❌ 没有找到可验证的文件');
                process.exit(1);
            }
            
            // 验证每个文件
            const results = [];
            for (const file of files) {
                const result = this.verifyFileSignature(file);
                const gatekeeperStatus = this.checkGatekeeperStatus(file);
                result.gatekeeper = gatekeeperStatus;
                results.push(result);
            }
            
            // 生成报告
            const report = this.generateReport(results);
            
            // 显示摘要
            this.displaySummary(report);
            
        } catch (error) {
            console.error('\n❌ 验证失败:', error.message);
            process.exit(1);
        }
    }
}

// 运行验证
if (require.main === module) {
    const verifier = new CodeSignVerifier();
    verifier.verify();
}

module.exports = CodeSignVerifier;
