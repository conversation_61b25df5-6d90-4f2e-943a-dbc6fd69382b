#!/usr/bin/env node

/**
 * 快速DMG打包脚本
 * 简化版本，适合快速构建和测试
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// 配置
const config = {
    projectRoot: path.join(__dirname, '..'),
    productName: '小梅花AI智能客服',
    version: '1.0.0',
    // 构建目标 (可选: 'x64', 'arm64', 'universal')
    target: process.argv[2] || 'universal'
};

// 日志函数
function log(message, type = 'info') {
    const colors = {
        info: '\x1b[36m',
        success: '\x1b[32m',
        warning: '\x1b[33m',
        error: '\x1b[31m',
        reset: '\x1b[0m'
    };
    
    const icons = {
        info: 'ℹ️',
        success: '✅',
        warning: '⚠️',
        error: '❌'
    };
    
    console.log(`${colors[type]}${icons[type]} ${message}${colors.reset}`);
}

// 执行命令
function exec(command, options = {}) {
    try {
        log(`执行: ${command}`, 'info');
        return execSync(command, { 
            encoding: 'utf8', 
            stdio: 'inherit',
            cwd: config.projectRoot,
            ...options 
        });
    } catch (error) {
        throw new Error(`命令执行失败: ${command}\n${error.message}`);
    }
}

// 主函数
async function buildDMG() {
    try {
        console.log('🚀 快速DMG打包开始...\n');
        
        // 1. 检查环境
        log('检查构建环境...', 'info');
        if (process.platform !== 'darwin') {
            log('警告: 不在macOS系统上运行', 'warning');
        }
        
        // 2. 安装依赖
        log('安装依赖...', 'info');
        exec('npm install');
        
        // 3. 清理旧文件
        log('清理旧文件...', 'info');
        const distDir = path.join(config.projectRoot, 'dist');
        if (fs.existsSync(distDir)) {
            fs.rmSync(distDir, { recursive: true, force: true });
        }
        
        // 4. 构建DMG
        log(`构建${config.target}版本...`, 'info');
        
        let buildCommand;
        switch (config.target) {
            case 'x64':
                buildCommand = 'npx electron-builder --mac --x64';
                break;
            case 'arm64':
                buildCommand = 'npx electron-builder --mac --arm64';
                break;
            case 'universal':
                buildCommand = 'npx electron-builder --mac --universal';
                break;
            default:
                buildCommand = 'npx electron-builder --mac';
        }
        
        exec(buildCommand);
        
        // 5. 检查结果
        log('检查构建结果...', 'info');
        const files = fs.readdirSync(distDir);
        const dmgFiles = files.filter(file => file.endsWith('.dmg'));
        
        if (dmgFiles.length === 0) {
            throw new Error('未找到DMG文件');
        }
        
        // 6. 显示结果
        log('构建完成！', 'success');
        console.log('\n📦 生成的文件:');
        dmgFiles.forEach(file => {
            const filePath = path.join(distDir, file);
            const stats = fs.statSync(filePath);
            const sizeInMB = (stats.size / 1024 / 1024).toFixed(1);
            console.log(`  ${file} (${sizeInMB}MB)`);
        });
        
        console.log(`\n📁 文件位置: ${distDir}`);
        
        // 7. 快速验证（仅macOS）
        if (process.platform === 'darwin') {
            log('验证DMG文件...', 'info');
            dmgFiles.forEach(file => {
                try {
                    exec(`hdiutil verify "${path.join(distDir, file)}"`, { stdio: 'pipe' });
                    log(`${file} 验证通过`, 'success');
                } catch (error) {
                    log(`${file} 验证失败，但可能仍然可用`, 'warning');
                }
            });
        }
        
        // 8. 打开文件夹（macOS）
        if (process.platform === 'darwin') {
            console.log('\n💡 打开文件夹查看结果:');
            console.log(`open "${distDir}"`);
            
            // 询问是否自动打开
            if (process.stdout.isTTY) {
                const readline = require('readline');
                const rl = readline.createInterface({
                    input: process.stdin,
                    output: process.stdout
                });
                
                rl.question('是否现在打开文件夹? (y/N): ', (answer) => {
                    if (answer.toLowerCase() === 'y' || answer.toLowerCase() === 'yes') {
                        exec(`open "${distDir}"`);
                    }
                    rl.close();
                });
            }
        }
        
        console.log('\n🎉 DMG打包完成！');
        
    } catch (error) {
        log(`构建失败: ${error.message}`, 'error');
        process.exit(1);
    }
}

// 显示帮助信息
function showHelp() {
    console.log(`
🔨 快速DMG打包工具

用法:
  node scripts/quick-dmg.js [target]

目标架构:
  x64        - Intel处理器版本
  arm64      - Apple Silicon版本  
  universal  - 通用版本 (默认)

示例:
  node scripts/quick-dmg.js x64
  node scripts/quick-dmg.js arm64
  node scripts/quick-dmg.js universal

注意:
  - 需要在macOS系统上运行以获得最佳体验
  - 确保已安装Xcode命令行工具
  - 首次运行会自动安装依赖
`);
}

// 检查命令行参数
if (process.argv.includes('--help') || process.argv.includes('-h')) {
    showHelp();
    process.exit(0);
}

// 运行构建
buildDMG();
