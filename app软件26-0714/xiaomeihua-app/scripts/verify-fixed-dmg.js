#!/usr/bin/env node

/**
 * 验证修复后的DMG文件脚本
 * 专门验证release/fixed目录中的修复版本
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

class FixedDMGVerifier {
    constructor() {
        this.projectRoot = path.resolve(__dirname, '..');
        this.fixedDir = path.join(this.projectRoot, 'release', 'fixed');
        
        this.packageJson = require(path.join(this.projectRoot, 'package.json'));
        this.productName = this.packageJson.productName;
        this.version = this.packageJson.version;
        
        this.log('🔍 修复后DMG验证工具');
        this.log(`产品名称: ${this.productName}`);
        this.log(`版本: ${this.version}`);
    }

    /**
     * 彩色日志输出
     */
    log(message, type = 'info') {
        const colors = {
            info: '\x1b[36m',
            success: '\x1b[32m',
            warning: '\x1b[33m',
            error: '\x1b[31m',
            step: '\x1b[35m',
            reset: '\x1b[0m'
        };
        
        const icons = {
            info: 'ℹ️',
            success: '✅',
            warning: '⚠️',
            error: '❌',
            step: '🔄'
        };
        
        const timestamp = new Date().toLocaleTimeString('zh-CN');
        console.log(`${colors[type]}[${timestamp}] ${icons[type]} ${message}${colors.reset}`);
    }

    /**
     * 执行命令
     */
    exec(command, options = {}) {
        try {
            const result = execSync(command, {
                encoding: 'utf8',
                stdio: options.silent ? 'pipe' : 'inherit',
                cwd: this.projectRoot,
                ...options
            });
            return result;
        } catch (error) {
            if (options.ignoreError) {
                return null;
            }
            throw new Error(`命令执行失败: ${command}\n${error.message}`);
        }
    }

    /**
     * 查找修复后的DMG文件
     */
    findFixedDMGFiles() {
        this.log('查找修复后的DMG文件...', 'step');
        
        if (!fs.existsSync(this.fixedDir)) {
            throw new Error('修复目录不存在: ' + this.fixedDir);
        }
        
        const files = fs.readdirSync(this.fixedDir);
        const dmgFiles = files.filter(file => file.endsWith('-fixed.dmg'));
        
        if (dmgFiles.length === 0) {
            throw new Error('未找到修复后的DMG文件');
        }
        
        this.log(`找到 ${dmgFiles.length} 个修复后的DMG文件:`);
        dmgFiles.forEach(file => {
            const filePath = path.join(this.fixedDir, file);
            const stats = fs.statSync(filePath);
            const sizeMB = (stats.size / 1024 / 1024).toFixed(1);
            this.log(`  ${file} (${sizeMB} MB)`);
        });
        
        return dmgFiles;
    }

    /**
     * 验证单个修复后的DMG文件
     */
    verifyFixedDMG(dmgFile) {
        this.log(`验证修复后的DMG: ${dmgFile}`, 'step');
        
        const dmgPath = path.join(this.fixedDir, dmgFile);
        const results = {
            file: dmgFile,
            architecture: this.extractArchitecture(dmgFile),
            dmgValid: false,
            appSigned: false,
            sealedResources: false,
            infoPlistBound: false,
            signatureValid: false,
            issues: [],
            status: 'unknown'
        };
        
        try {
            // 验证DMG完整性
            this.exec(`hdiutil verify "${dmgPath}"`, { silent: true });
            results.dmgValid = true;
            this.log('✅ DMG完整性验证通过', 'success');
            
            // 挂载DMG并检查应用程序
            const mountResult = this.exec(`hdiutil attach "${dmgPath}" -readonly -nobrowse`, { silent: true });
            const mountLines = mountResult.split('\n').filter(line => line.includes('/Volumes/'));
            
            if (mountLines.length === 0) {
                throw new Error('无法挂载DMG');
            }
            
            const mountPoint = mountLines[0].split('\t').pop().trim();
            
            try {
                const appName = `${this.productName}.app`;
                const appPath = path.join(mountPoint, appName);
                
                if (!fs.existsSync(appPath)) {
                    throw new Error('应用程序不存在');
                }
                
                // 检查应用程序签名
                const signInfo = this.exec(`codesign -dv --verbose=4 "${appPath}" 2>&1`, { silent: true });
                results.appSigned = true;
                
                // 检查资源封装
                if (signInfo.includes('Sealed Resources version=')) {
                    results.sealedResources = true;
                    this.log('✅ 资源封装正常', 'success');
                } else {
                    results.sealedResources = false;
                    results.issues.push('资源封装缺失');
                }
                
                // 检查Info.plist绑定
                if (signInfo.includes('Info.plist entries=')) {
                    results.infoPlistBound = true;
                    this.log('✅ Info.plist绑定正常', 'success');
                } else {
                    results.infoPlistBound = false;
                    results.issues.push('Info.plist未绑定');
                }
                
                // 验证签名有效性
                try {
                    this.exec(`codesign --verify "${appPath}"`, { silent: true });
                    results.signatureValid = true;
                    this.log('✅ 签名验证通过', 'success');
                } catch (error) {
                    results.signatureValid = false;
                    results.issues.push('签名验证失败');
                }
                
                // 确定状态
                if (results.sealedResources && results.infoPlistBound && results.signatureValid) {
                    results.status = 'fixed';
                    this.log('🎉 修复成功！', 'success');
                } else {
                    results.status = 'partial';
                    this.log('⚠️ 部分修复', 'warning');
                }
                
            } finally {
                // 卸载DMG
                this.exec(`hdiutil detach "${mountPoint}"`, { ignoreError: true });
            }
            
        } catch (error) {
            results.issues.push(`验证失败: ${error.message}`);
            results.status = 'failed';
            this.log(`❌ 验证失败: ${error.message}`, 'error');
        }
        
        return results;
    }

    /**
     * 从文件名提取架构信息
     */
    extractArchitecture(filename) {
        if (filename.includes('x64')) return 'x64';
        if (filename.includes('arm64')) return 'arm64';
        if (filename.includes('universal')) return 'universal';
        return 'unknown';
    }

    /**
     * 生成验证报告
     */
    generateReport(verificationResults) {
        this.log('生成验证报告...', 'step');
        
        const report = {
            timestamp: new Date().toISOString(),
            productName: this.productName,
            version: this.version,
            summary: {
                totalFiles: verificationResults.length,
                fixedFiles: 0,
                partialFiles: 0,
                failedFiles: 0
            },
            results: verificationResults,
            overallStatus: 'unknown',
            recommendations: []
        };
        
        // 分析结果
        verificationResults.forEach(result => {
            switch (result.status) {
                case 'fixed':
                    report.summary.fixedFiles++;
                    break;
                case 'partial':
                    report.summary.partialFiles++;
                    break;
                case 'failed':
                    report.summary.failedFiles++;
                    break;
            }
        });
        
        // 确定整体状态
        if (report.summary.fixedFiles === report.summary.totalFiles) {
            report.overallStatus = 'all-fixed';
            report.recommendations.push('所有DMG文件修复成功，可以发布使用');
            report.recommendations.push('用户安装时应该看到"无法验证"而不是"已损坏"');
        } else if (report.summary.fixedFiles > 0) {
            report.overallStatus = 'partially-fixed';
            report.recommendations.push('部分DMG文件修复成功，建议重新修复失败的文件');
        } else {
            report.overallStatus = 'not-fixed';
            report.recommendations.push('所有DMG文件修复失败，需要检查修复流程');
        }
        
        // 保存报告
        const reportPath = path.join(this.fixedDir, 'verification-report-fixed.json');
        fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
        
        this.log(`验证报告已保存: ${reportPath}`, 'success');
        return report;
    }

    /**
     * 显示验证结果
     */
    showResults(report) {
        console.log('\n' + '='.repeat(60));
        this.log('🔍 修复后DMG验证结果', 'step');
        console.log('='.repeat(60));
        
        console.log(`\n📊 验证摘要:`);
        console.log(`  总文件数: ${report.summary.totalFiles}`);
        console.log(`  完全修复: ${report.summary.fixedFiles}`);
        console.log(`  部分修复: ${report.summary.partialFiles}`);
        console.log(`  修复失败: ${report.summary.failedFiles}`);
        console.log(`  整体状态: ${report.overallStatus}`);
        
        console.log(`\n📋 详细结果:`);
        report.results.forEach(result => {
            const statusIcon = {
                'fixed': '✅',
                'partial': '⚠️',
                'failed': '❌',
                'unknown': '❓'
            }[result.status] || '❓';
            
            console.log(`\n  ${statusIcon} ${result.file} (${result.architecture}):`);
            console.log(`    DMG完整性: ${result.dmgValid ? '✅' : '❌'}`);
            console.log(`    应用签名: ${result.appSigned ? '✅' : '❌'}`);
            console.log(`    资源封装: ${result.sealedResources ? '✅' : '❌'}`);
            console.log(`    Info.plist: ${result.infoPlistBound ? '✅' : '❌'}`);
            console.log(`    签名验证: ${result.signatureValid ? '✅' : '❌'}`);
            
            if (result.issues.length > 0) {
                console.log(`    问题:`);
                result.issues.forEach(issue => console.log(`      - ${issue}`));
            }
        });
        
        console.log(`\n💡 建议:`);
        report.recommendations.forEach(rec => {
            console.log(`  - ${rec}`);
        });
        
        if (report.overallStatus === 'all-fixed') {
            console.log(`\n🎉 所有DMG文件修复成功！`);
            console.log(`\n📁 修复后文件位置: ${this.fixedDir}`);
            console.log(`\n🚀 可以发布的文件:`);
            report.results.forEach(result => {
                if (result.status === 'fixed') {
                    console.log(`  - ${result.file} (${result.architecture})`);
                }
            });
        }
    }

    /**
     * 主验证流程
     */
    async verify() {
        try {
            console.log('🔍 开始验证修复后的DMG文件...\n');
            
            const dmgFiles = this.findFixedDMGFiles();
            const verificationResults = [];
            
            // 验证所有修复后的DMG文件
            for (const dmgFile of dmgFiles) {
                const result = this.verifyFixedDMG(dmgFile);
                verificationResults.push(result);
            }
            
            const report = this.generateReport(verificationResults);
            this.showResults(report);
            
            return report.overallStatus === 'all-fixed';
            
        } catch (error) {
            this.log(`验证失败: ${error.message}`, 'error');
            console.error('\n详细错误信息:');
            console.error(error.stack);
            return false;
        }
    }
}

// 主程序入口
async function main() {
    const verifier = new FixedDMGVerifier();
    const success = await verifier.verify();
    process.exit(success ? 0 : 1);
}

// 如果直接运行此脚本
if (require.main === module) {
    main();
}

module.exports = FixedDMGVerifier;
