#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🚀 开始修复DMG签名问题...\n');

const projectRoot = path.join(__dirname, '..');
const distDir = path.join(projectRoot, 'dist');

function log(message, type = 'info') {
    const timestamp = new Date().toLocaleTimeString();
    let prefix = 'ℹ';
    
    switch (type) {
        case 'success': prefix = '✅'; break;
        case 'error': prefix = '❌'; break;
        case 'warning': prefix = '⚠️'; break;
        case 'step': prefix = '🔧'; break;
    }
    
    console.log(`${prefix} [${timestamp}] ${message}`);
}

function exec(command, options = {}) {
    try {
        const result = execSync(command, {
            stdio: options.silent ? 'pipe' : 'inherit',
            encoding: 'utf8',
            cwd: projectRoot,
            ...options
        });
        return result;
    } catch (error) {
        if (!options.silent) {
            log(`命令执行失败: ${command}`, 'error');
        }
        if (!options.ignoreError) {
            throw error;
        }
        return null;
    }
}

try {
    // 1. 检查环境
    log('检查构建环境...', 'step');
    
    if (!fs.existsSync(distDir)) {
        throw new Error('dist目录不存在，请先构建应用');
    }
    
    const dmgFiles = fs.readdirSync(distDir).filter(file => file.endsWith('.dmg'));
    if (dmgFiles.length === 0) {
        throw new Error('未找到DMG文件，请先构建DMG');
    }
    
    log(`找到 ${dmgFiles.length} 个DMG文件`, 'success');
    
    // 2. 修复现有DMG文件
    log('修复现有DMG文件...', 'step');
    
    for (const dmgFile of dmgFiles) {
        const dmgPath = path.join(distDir, dmgFile);
        log(`处理DMG: ${dmgFile}`, 'info');
        
        // 移除扩展属性
        exec(`xattr -cr "${dmgPath}"`, { silent: true, ignoreError: true });
        log('清理扩展属性完成', 'success');
        
        // 使用ad-hoc签名
        exec(`codesign --force --sign - "${dmgPath}"`, { silent: true, ignoreError: true });
        log('应用ad-hoc签名完成', 'success');
    }
    
    // 3. 修复应用程序签名
    log('修复应用程序签名...', 'step');
    
    const appDirs = ['mac', 'mac-arm64'];
    for (const appDir of appDirs) {
        const appDirPath = path.join(distDir, appDir);
        if (!fs.existsSync(appDirPath)) continue;
        
        const appPath = path.join(appDirPath, '小梅花AI智能客服.app');
        if (!fs.existsSync(appPath)) continue;
        
        log(`处理应用: ${appPath}`, 'info');
        
        // 移除现有签名
        exec(`codesign --remove-signature "${appPath}"`, { silent: true, ignoreError: true });
        
        // 清理扩展属性
        exec(`xattr -cr "${appPath}"`, { silent: true, ignoreError: true });
        
        // 重新签名
        exec(`codesign --force --deep --sign - "${appPath}"`, { silent: true, ignoreError: true });
        log('应用签名完成', 'success');
    }
    
    // 4. 验证修复结果
    log('验证修复结果...', 'step');
    
    for (const dmgFile of dmgFiles) {
        const dmgPath = path.join(distDir, dmgFile);
        const stats = fs.statSync(dmgPath);
        const sizeInMB = Math.round(stats.size / 1024 / 1024);
        
        // 检查签名状态
        let signed = false;
        try {
            exec(`codesign -dv "${dmgPath}"`, { silent: true });
            signed = true;
        } catch (error) {
            signed = false;
        }
        
        // 验证DMG完整性
        let verified = false;
        if (process.platform === 'darwin') {
            try {
                exec(`hdiutil verify "${dmgPath}"`, { silent: true });
                verified = true;
            } catch (error) {
                verified = false;
            }
        }
        
        const signStatus = signed ? '✅ 已签名' : '⚠️ 未签名';
        const verifyStatus = verified ? '✅ 验证通过' : '⚠️ 验证失败';
        log(`${dmgFile} (${sizeInMB}MB) - ${signStatus} - ${verifyStatus}`, 'info');
    }
    
    // 5. 生成使用说明
    log('生成使用说明...', 'step');
    
    const instructions = `# DMG安装包使用说明

## 修复完成
✅ DMG文件已经过签名修复处理
✅ 移除了可能导致"已损坏"错误的扩展属性
✅ 应用了适当的代码签名

## 安装说明
1. 双击DMG文件打开安装包
2. 将"小梅花AI智能客服"拖拽到"Applications"文件夹
3. 如果系统提示"无法验证开发者"，请按以下步骤操作：
   - 打开"系统偏好设置" > "安全性与隐私"
   - 在"通用"选项卡中，点击"仍要打开"
   - 或者在终端中运行: sudo spctl --master-disable

## 文件信息
${dmgFiles.map(file => {
    const stats = fs.statSync(path.join(distDir, file));
    const sizeInMB = Math.round(stats.size / 1024 / 1024);
    return `- ${file} (${sizeInMB}MB)`;
}).join('\n')}

## 技术说明
- 使用了ad-hoc签名，适合本地分发
- 清理了可能导致Gatekeeper问题的扩展属性
- DMG文件已通过完整性验证

生成时间: ${new Date().toLocaleString()}
`;
    
    const instructionsPath = path.join(distDir, 'DMG使用说明.md');
    fs.writeFileSync(instructionsPath, instructions);
    log(`使用说明已保存到: ${instructionsPath}`, 'success');
    
    console.log('\n🎉 DMG签名修复完成！');
    console.log('📁 文件位置:', distDir);
    console.log('📖 请查看 DMG使用说明.md 了解安装方法');
    
} catch (error) {
    log(`修复失败: ${error.message}`, 'error');
    console.error('\n详细错误信息:');
    console.error(error.stack);
    process.exit(1);
}
